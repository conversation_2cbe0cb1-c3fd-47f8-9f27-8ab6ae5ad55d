accelerate==1.7.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.11
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
asttokens==3.0.0
async-timeout==5.0.1
attrs==25.3.0
bitsandbytes==0.46.0
certifi==2025.4.26
charset-normalizer==3.4.2
click==8.1.8
comm==0.2.2
datasets==3.6.0
debugpy==1.8.14
decorator==5.2.1
dill==0.3.8
distro==1.9.0
docstring-parser==0.16
exceptiongroup==1.3.0
executing==2.2.0
fastapi==0.115.12
filelock==3.18.0
frozenlist==1.6.2
fsspec==2025.3.0
groq==0.28.0
grpcio==1.73.0
grpcio-tools==1.73.0
grpclib==0.4.7
h11==0.16.0
h2==4.2.0
hf-xet==1.1.3
hpack==4.1.0
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.32.4
hyperframe==6.1.0
idna==3.10
instructor==1.8.3
ipykernel==6.29.5
ipython==8.37.0
jedi==0.19.2
jinja2==3.1.6
jiter==0.8.2
joblib==1.5.1
jupyter-client==8.6.3
jupyter-core==5.8.1
markdown-it-py==3.0.0
markupsafe==3.0.2
matplotlib-inline==0.1.7
mdurl==0.1.2
modal==1.0.3
mpmath==1.3.0
multidict==6.4.4
multiprocess==0.70.16
nest-asyncio==1.6.0
networkx==3.4.2
numpy==2.2.6
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-cufile-cu12==********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==********
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
openai==1.86.0
packaging==25.0
pandas==2.3.0
parso==0.8.4
pexpect==4.9.0
pip==25.1.1
platformdirs==4.3.8
prompt-toolkit==3.0.51
propcache==0.3.1
protobuf==6.31.1
psutil==7.0.0
ptyprocess==0.7.0
pure-eval==0.2.3
pyarrow==20.0.0
pydantic==2.11.5
pydantic-core==2.33.2
pygments==2.19.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytz==2025.2
pyyaml==6.0.2
pyzmq==26.4.0
regex==2024.11.6
requests==2.32.3
rich==13.9.4
safetensors==0.5.3
scikit-learn==1.7.0
scipy==1.15.3
setuptools==80.9.0
shellingham==1.5.4
sigtools==4.0.1
six==1.17.0
sniffio==1.3.1
stack-data==0.6.3
starlette==0.46.2
sympy==1.14.0
synchronicity==0.9.13
tenacity==9.1.2
threadpoolctl==3.6.0
tokenizers==0.21.1
toml==0.10.2
torch==2.7.1
tornado==6.5.1
tqdm==4.67.1
traitlets==5.14.3
transformers==4.52.4
triton==3.3.1
trl==0.18.1
typer==0.16.0
types-certifi==2021.10.8.3
types-toml==0.10.8.20240310
typing-extensions==4.14.0
typing-inspection==0.4.1
tzdata==2025.2
urllib3==2.4.0
watchfiles==1.0.5
wcwidth==0.2.13
wheel==0.45.1
xxhash==3.5.0
yarl==1.20.0
