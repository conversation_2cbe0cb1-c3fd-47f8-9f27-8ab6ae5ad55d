#!/usr/bin/env python3
"""
FinQA Student Model Inference
Runs student model inference on FinQA dataset with incremental logging.
"""

import os
import sys
import torch
import pandas as pd
import numpy as np
from transformers import AutoModelForCausalLM, AutoTokenizer
from datasets import load_dataset
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import warnings
from typing import Dict, List, Any, Optional
import argparse
from datetime import datetime
import json
import re

warnings.filterwarnings("ignore")

class FinqaStudentEvaluator:
    def __init__(self, model_path: str, device: str = "auto"):
        """Initialize student model for FinQA evaluation"""
        self.device = device if device != "auto" else ("cuda" if torch.cuda.is_available() else "cpu")
        self.model_path = model_path
        
        print(f"Loading student model from: {model_path}")
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            device_map="auto" if torch.cuda.device_count() > 1 else self.device
        )
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        if not self.tokenizer.pad_token:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
        self.model.eval()
        print(f"Student model loaded on {self.device}")
        
        # Dataset context
        self.dataset_context = """
        FinQA is a financial question answering dataset. Given a question
        about financial data and relevant context, the task is to provide
        a numerical answer or calculation result.
        """
    
    def generate_response(self, prompt: str, max_length: int = 100, temperature: float = 0.1) -> str:
        """Generate response from student model"""
        inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=temperature,
                do_sample=True if temperature > 0 else False,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # Decode only the generated part
        generated_tokens = outputs[0][inputs['input_ids'].shape[1]:]
        response = self.tokenizer.decode(generated_tokens, skip_special_tokens=True)
        return response.strip()
    
    def format_prompt(self, case_data: Dict) -> str:
        """Format case data into prompt for student model"""
        context = case_data.get('context', case_data.get('pre_text', ''))
        table = case_data.get('table', '')
        
        prompt = f"""
        {self.dataset_context}
        Given the following question from the FinQA dataset: 
        Question: {case_data['question']}
        Context: {context}
        Table: {table}
        
        Please provide a numerical answer to this question.
        Answer:"""
        return prompt
    
    def extract_answer(self, response: str) -> Optional[float]:
        """Extract numerical answer from model response"""
        # Look for numbers in the response
        numbers = re.findall(r'-?\d+\.?\d*', response)
        if numbers:
            try:
                return float(numbers[0])
            except ValueError:
                pass
        return None
    
    def is_correct(self, predicted: float, actual: float, tolerance: float = 0.01) -> bool:
        """Check if prediction is correct within tolerance"""
        return abs(predicted - actual) < tolerance
    
    def save_incremental_metrics(self, metrics_data: List[Dict], output_file: str):
        """Save metrics incrementally to CSV file"""
        df = pd.DataFrame(metrics_data)
        df.to_csv(output_file, index=False)
        print(f"Metrics saved to {output_file}")
    
    def evaluate(self, dataset_size: int = 1000, log_interval: int = 10, output_file: str = "student_metrics.csv"):
        """Evaluate student model on FinQA dataset with incremental logging"""
        print(f"Evaluating FinQA dataset (size: {dataset_size})")
        print(f"Logging every {log_interval} samples to {output_file}")
        
        # Load dataset
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "finqa")
        data = ds['train'].select(range(min(dataset_size, len(ds['train']))))
        
        correct_predictions = 0
        total_valid = 0
        invalid_cases = 0
        metrics_log = []
        predictions = []
        actuals = []
        
        for i, case_data in enumerate(data):
            print(f"Processing case {i+1}/{len(data)}")
            
            prompt = self.format_prompt(case_data)
            response = self.generate_response(prompt, max_length=100, temperature=0.1)
            predicted_answer = self.extract_answer(response)
            
            if predicted_answer is not None:
                # Get actual answer
                actual_answer = float(case_data.get('answer', case_data.get('exe_ans', 0)))
                actuals.append(actual_answer)
                predictions.append(predicted_answer)
                
                is_correct = self.is_correct(predicted_answer, actual_answer)
                if is_correct:
                    correct_predictions += 1
                total_valid += 1
                
                print(f"Case {i+1}: Predicted={predicted_answer}, Actual={actual_answer}, Correct={is_correct}")
            else:
                invalid_cases += 1
                print(f"Case {i+1}: Invalid response - {response}")
            
            # Log metrics every log_interval samples
            if (i + 1) % log_interval == 0 or (i + 1) == len(data):
                if total_valid > 0:
                    accuracy = (correct_predictions / total_valid) * 100
                    
                    # For numerical tasks, precision/recall/f1 are same as accuracy
                    precision = accuracy
                    recall = accuracy
                    f1 = accuracy
                    f1_macro = accuracy
                    
                    metrics_entry = {
                        "Sample": i + 1,
                        "Accuracy": round(accuracy, 3),
                        "Precision": round(precision, 3),
                        "Recall": round(recall, 3),
                        "F1 Score": round(f1, 3),
                        "Macro F1 Score": round(f1_macro, 3),
                        "Correct Predictions": correct_predictions,
                        "Valid Cases": total_valid,
                        "Invalid Cases": invalid_cases,
                        "Total Cases": i + 1,
                        "Timestamp": datetime.now().isoformat(),
                        "Model Path": self.model_path
                    }
                    
                    metrics_log.append(metrics_entry)
                    
                    # Save incremental results
                    self.save_incremental_metrics(metrics_log, output_file)
                    
                    print(f"=== Metrics at sample {i+1} ===")
                    print(f"Accuracy: {accuracy:.2f}%")
                    print(f"Correct Predictions: {correct_predictions}")
                    print(f"Valid Cases: {total_valid}/{i+1}")
                    print(f"Invalid Cases: {invalid_cases}")
                    print("=" * 30)
        
        # Final metrics
        if total_valid > 0:
            final_accuracy = (correct_predictions / total_valid) * 100
            
            print(f"\n{'='*50}")
            print("FINAL FINQA RESULTS")
            print(f"{'='*50}")
            print(f"Final Accuracy: {final_accuracy:.2f}%")
            print(f"Correct Predictions: {correct_predictions}")
            print(f"Valid Cases: {total_valid}")
            print(f"Invalid Cases: {invalid_cases}")
            print(f"Total Cases: {len(data)}")
            
            # Calculate some statistics on predictions vs actuals
            if len(predictions) > 0 and len(actuals) > 0:
                pred_array = np.array(predictions)
                actual_array = np.array(actuals)
                
                mae = np.mean(np.abs(pred_array - actual_array))
                mse = np.mean((pred_array - actual_array) ** 2)
                rmse = np.sqrt(mse)
                
                print(f"Mean Absolute Error: {mae:.4f}")
                print(f"Root Mean Square Error: {rmse:.4f}")
            
            # Save final summary
            final_summary = {
                "dataset": "finqa",
                "model_path": self.model_path,
                "final_accuracy": final_accuracy,
                "correct_predictions": correct_predictions,
                "valid_cases": total_valid,
                "invalid_cases": invalid_cases,
                "total_cases": len(data),
                "timestamp": datetime.now().isoformat()
            }
            
            if len(predictions) > 0:
                final_summary.update({
                    "mean_absolute_error": float(mae),
                    "root_mean_square_error": float(rmse),
                    "sample_predictions": predictions[:10],  # First 10 predictions
                    "sample_actuals": actuals[:10]  # First 10 actual values
                })
            
            summary_file = output_file.replace('.csv', '_summary.json')
            with open(summary_file, 'w') as f:
                json.dump(final_summary, f, indent=2)
            
            print(f"Final summary saved to {summary_file}")
            
        return metrics_log

def main():
    parser = argparse.ArgumentParser(description='Run student model inference on FinQA dataset')
    parser.add_argument('--model-path', required=True, help='Path to the distilled student model')
    parser.add_argument('--dataset-size', type=int, default=1000, help='Number of samples to evaluate')
    parser.add_argument('--log-interval', type=int, default=10, help='Log metrics every N samples')
    parser.add_argument('--output-file', default='student_metrics.csv', help='Output metrics file')
    parser.add_argument('--device', default='auto', help='Device to run inference on')
    
    args = parser.parse_args()
    
    print("="*60)
    print("FINQA STUDENT MODEL EVALUATION")
    print("="*60)
    print(f"Model: {args.model_path}")
    print(f"Dataset size: {args.dataset_size}")
    print(f"Log interval: {args.log_interval}")
    print(f"Output file: {args.output_file}")
    print(f"Device: {args.device}")
    print("="*60)
    
    # Initialize evaluator
    evaluator = FinqaStudentEvaluator(args.model_path, args.device)
    
    # Run evaluation
    metrics_log = evaluator.evaluate(
        dataset_size=args.dataset_size,
        log_interval=args.log_interval,
        output_file=args.output_file
    )
    
    print(f"\nFinQA evaluation completed!")
    print(f"Results saved to: {args.output_file}")

if __name__ == "__main__":
    main()
