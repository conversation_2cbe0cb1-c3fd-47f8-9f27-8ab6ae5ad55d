#!/usr/bin/env python3
"""
PubMedQA Student Model Inference
Runs student model inference on PubMedQA dataset with incremental logging.
"""

import os
import sys
import torch
import pandas as pd
import numpy as np
from transformers import AutoModelForCausalLM, AutoTokenizer
from datasets import load_dataset
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import warnings
from typing import Dict, List, Any, Optional
import argparse
from datetime import datetime
import json

warnings.filterwarnings("ignore")

class PubmedStudentEvaluator:
    def __init__(self, model_path: str, device: str = "auto"):
        """Initialize student model for PubMedQA evaluation"""
        self.device = device if device != "auto" else ("cuda" if torch.cuda.is_available() else "cpu")
        self.model_path = model_path
        
        print(f"Loading student model from: {model_path}")
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            device_map="auto" if torch.cuda.device_count() > 1 else self.device
        )
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        if not self.tokenizer.pad_token:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
        self.model.eval()
        print(f"Student model loaded on {self.device}")
        
        # Dataset context
        self.dataset_context = """
        PubMedQA is a biomedical question answering dataset. Given a question
        and relevant context from PubMed abstracts, the task is to answer
        whether the statement is true (yes), false (no), or uncertain (maybe).
        """
    
    def generate_response(self, prompt: str, max_length: int = 50, temperature: float = 0.1) -> str:
        """Generate response from student model"""
        inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=temperature,
                do_sample=True if temperature > 0 else False,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # Decode only the generated part
        generated_tokens = outputs[0][inputs['input_ids'].shape[1]:]
        response = self.tokenizer.decode(generated_tokens, skip_special_tokens=True)
        return response.strip()
    
    def format_prompt(self, case_data: Dict) -> str:
        """Format case data into prompt for student model"""
        prompt = f"""
        {self.dataset_context}
        Given the following question from the PubMedQA dataset: 
        Question: {case_data['question']}
        Context: {case_data['context']}
        Long Answer: {case_data['long_answer']}
        
        Please provide the final decision based on these data. It must be either "yes", "no", or "maybe".
        Answer:"""
        return prompt
    
    def extract_answer(self, response: str) -> Optional[str]:
        """Extract answer from model response"""
        response = response.strip().lower()
        
        # Look for exact matches first
        if 'yes' in response and 'no' not in response:
            return 'yes'
        elif 'no' in response and 'yes' not in response:
            return 'no'
        elif 'maybe' in response:
            return 'maybe'
        
        return None
    
    def save_incremental_metrics(self, metrics_data: List[Dict], output_file: str):
        """Save metrics incrementally to CSV file"""
        df = pd.DataFrame(metrics_data)
        df.to_csv(output_file, index=False)
        print(f"Metrics saved to {output_file}")
    
    def evaluate(self, dataset_size: int = 1000, log_interval: int = 10, output_file: str = "student_metrics.csv"):
        """Evaluate student model on PubMedQA dataset with incremental logging"""
        print(f"Evaluating PubMedQA dataset (size: {dataset_size})")
        print(f"Logging every {log_interval} samples to {output_file}")
        
        # Load dataset
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "pubmedqa")
        data = ds['train'].select(range(min(dataset_size, len(ds['train']))))
        
        y_true = []
        y_pred = []
        invalid_cases = 0
        metrics_log = []
        
        for i, case_data in enumerate(data):
            print(f"Processing case {i+1}/{len(data)}")
            
            prompt = self.format_prompt(case_data)
            response = self.generate_response(prompt, max_length=50, temperature=0.1)
            predicted_answer = self.extract_answer(response)
            
            if predicted_answer is not None:
                y_true.append(case_data['final_decision'])
                y_pred.append(predicted_answer)
                print(f"Case {i+1}: Predicted={predicted_answer}, Actual={case_data['final_decision']}, Correct={predicted_answer == case_data['final_decision']}")
            else:
                invalid_cases += 1
                print(f"Case {i+1}: Invalid response - {response}")
            
            # Log metrics every log_interval samples
            if (i + 1) % log_interval == 0 or (i + 1) == len(data):
                if len(y_true) > 0:
                    accuracy = accuracy_score(y_true, y_pred) * 100
                    precision = precision_score(y_true, y_pred, average='weighted', zero_division=0) * 100
                    recall = recall_score(y_true, y_pred, average='weighted', zero_division=0) * 100
                    f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0) * 100
                    f1_macro = f1_score(y_true, y_pred, average='macro', zero_division=0) * 100
                    
                    metrics_entry = {
                        "Sample": i + 1,
                        "Accuracy": round(accuracy, 3),
                        "Precision": round(precision, 3),
                        "Recall": round(recall, 3),
                        "F1 Score": round(f1, 3),
                        "Macro F1 Score": round(f1_macro, 3),
                        "Valid Cases": len(y_true),
                        "Invalid Cases": invalid_cases,
                        "Total Cases": i + 1,
                        "Timestamp": datetime.now().isoformat(),
                        "Model Path": self.model_path
                    }
                    
                    metrics_log.append(metrics_entry)
                    
                    # Save incremental results
                    self.save_incremental_metrics(metrics_log, output_file)
                    
                    print(f"=== Metrics at sample {i+1} ===")
                    print(f"Accuracy: {accuracy:.2f}%")
                    print(f"Precision: {precision:.2f}%")
                    print(f"Recall: {recall:.2f}%")
                    print(f"F1 Score: {f1:.2f}%")
                    print(f"Macro F1: {f1_macro:.2f}%")
                    print(f"Valid Cases: {len(y_true)}/{i+1}")
                    print(f"Invalid Cases: {invalid_cases}")
                    print("=" * 30)
        
        # Final metrics
        if len(y_true) > 0:
            final_accuracy = accuracy_score(y_true, y_pred) * 100
            final_precision = precision_score(y_true, y_pred, average='weighted', zero_division=0) * 100
            final_recall = recall_score(y_true, y_pred, average='weighted', zero_division=0) * 100
            final_f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0) * 100
            final_f1_macro = f1_score(y_true, y_pred, average='macro', zero_division=0) * 100
            conf_matrix = confusion_matrix(y_true, y_pred)
            
            print(f"\n{'='*50}")
            print("FINAL PUBMEDQA RESULTS")
            print(f"{'='*50}")
            print(f"Final Accuracy: {final_accuracy:.2f}%")
            print(f"Final Precision: {final_precision:.2f}%")
            print(f"Final Recall: {final_recall:.2f}%")
            print(f"Final F1 Score: {final_f1:.2f}%")
            print(f"Final Macro F1 Score: {final_f1_macro:.2f}%")
            print(f"Valid Cases: {len(y_true)}")
            print(f"Invalid Cases: {invalid_cases}")
            print(f"Total Cases: {len(data)}")
            print("Confusion Matrix:")
            print(conf_matrix)
            
            # Save final summary
            final_summary = {
                "dataset": "pubmedqa",
                "model_path": self.model_path,
                "final_accuracy": final_accuracy,
                "final_precision": final_precision,
                "final_recall": final_recall,
                "final_f1_score": final_f1,
                "final_f1_macro": final_f1_macro,
                "valid_cases": len(y_true),
                "invalid_cases": invalid_cases,
                "total_cases": len(data),
                "confusion_matrix": conf_matrix.tolist(),
                "timestamp": datetime.now().isoformat()
            }
            
            summary_file = output_file.replace('.csv', '_summary.json')
            with open(summary_file, 'w') as f:
                json.dump(final_summary, f, indent=2)
            
            print(f"Final summary saved to {summary_file}")
            
        return metrics_log

def main():
    parser = argparse.ArgumentParser(description='Run student model inference on PubMedQA dataset')
    parser.add_argument('--model-path', required=True, help='Path to the distilled student model')
    parser.add_argument('--dataset-size', type=int, default=1000, help='Number of samples to evaluate')
    parser.add_argument('--log-interval', type=int, default=10, help='Log metrics every N samples')
    parser.add_argument('--output-file', default='student_metrics.csv', help='Output metrics file')
    parser.add_argument('--device', default='auto', help='Device to run inference on')
    
    args = parser.parse_args()
    
    print("="*60)
    print("PUBMEDQA STUDENT MODEL EVALUATION")
    print("="*60)
    print(f"Model: {args.model_path}")
    print(f"Dataset size: {args.dataset_size}")
    print(f"Log interval: {args.log_interval}")
    print(f"Output file: {args.output_file}")
    print(f"Device: {args.device}")
    print("="*60)
    
    # Initialize evaluator
    evaluator = PubmedStudentEvaluator(args.model_path, args.device)
    
    # Run evaluation
    metrics_log = evaluator.evaluate(
        dataset_size=args.dataset_size,
        log_interval=args.log_interval,
        output_file=args.output_file
    )
    
    print(f"\nPubMedQA evaluation completed!")
    print(f"Results saved to: {args.output_file}")

if __name__ == "__main__":
    main()
