#!/bin/bash
"""
Run All Student Model Evaluations
Runs student model evaluation on all benchmark datasets in separate tmux sessions.
"""

# Configuration
MODEL_PATH="${1:-./distilled_student/final_student_model}"
DATASET_SIZE="${2:-1000}"
LOG_INTERVAL="${3:-10}"

# Check if model path is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <model_path> [dataset_size] [log_interval]"
    echo "Example: $0 ./distilled_student/final_student_model 1000 10"
    echo ""
    echo "Using default model path: $MODEL_PATH"
fi

echo "=============================================="
echo "RUNNING ALL STUDENT MODEL EVALUATIONS"
echo "=============================================="
echo "Model Path: $MODEL_PATH"
echo "Dataset Size: $DATASET_SIZE samples per dataset"
echo "Log Interval: Every $LOG_INTERVAL samples"
echo "=============================================="

# Check if model exists
if [ ! -d "$MODEL_PATH" ] && [ ! -f "$MODEL_PATH/config.json" ]; then
    echo "❌ Model not found at: $MODEL_PATH"
    echo "Please provide a valid model path."
    exit 1
fi

echo "🔍 Model found at: $MODEL_PATH"
echo ""

# Make scripts executable
chmod +x run_casehold_evaluation.sh
chmod +x run_pubmed_evaluation.sh
chmod +x run_finqa_evaluation.sh

# Function to check if tmux session exists
session_exists() {
    tmux has-session -t "$1" 2>/dev/null
}

# Function to start evaluation
start_evaluation() {
    local dataset=$1
    local script=$2
    local session=$3
    
    echo "🚀 Starting $dataset evaluation..."
    
    if session_exists "$session"; then
        echo "⚠️  Session '$session' already exists. Skipping $dataset."
        return 1
    fi
    
    # Run the evaluation script non-interactively
    echo "y" | ./$script "$MODEL_PATH" "$DATASET_SIZE" "$LOG_INTERVAL" > /dev/null
    
    if session_exists "$session"; then
        echo "✅ $dataset evaluation started in session '$session'"
        return 0
    else
        echo "❌ Failed to start $dataset evaluation"
        return 1
    fi
}

# Start all evaluations
echo "Starting evaluations in parallel tmux sessions..."
echo ""

# Start CaseHOLD evaluation
start_evaluation "CaseHOLD" "run_casehold_evaluation.sh" "casehold_eval"
sleep 2

# Start PubMedQA evaluation
start_evaluation "PubMedQA" "run_pubmed_evaluation.sh" "pubmed_eval"
sleep 2

# Start FinQA evaluation
start_evaluation "FinQA" "run_finqa_evaluation.sh" "finqa_eval"
sleep 2

echo ""
echo "=============================================="
echo "ALL EVALUATIONS STARTED"
echo "=============================================="

# Show running sessions
echo "📊 Active tmux sessions:"
tmux list-sessions | grep -E "(casehold_eval|pubmed_eval|finqa_eval)" || echo "No evaluation sessions found"

echo ""
echo "📁 Output files will be saved to:"
echo "  benchmark/casehold/student_metrics.csv"
echo "  benchmark/pubmed/student_metrics.csv"
echo "  benchmark/finqa/student_metrics.csv"

echo ""
echo "🔧 Monitoring commands:"
echo "  # Attach to specific evaluation:"
echo "  tmux attach-session -t casehold_eval"
echo "  tmux attach-session -t pubmed_eval"
echo "  tmux attach-session -t finqa_eval"
echo ""
echo "  # Check progress:"
echo "  tail -f benchmark/casehold/student_metrics.csv"
echo "  tail -f benchmark/pubmed/student_metrics.csv"
echo "  tail -f benchmark/finqa/student_metrics.csv"
echo ""
echo "  # List all sessions:"
echo "  tmux list-sessions"
echo ""
echo "  # Kill all evaluation sessions:"
echo "  tmux kill-session -t casehold_eval"
echo "  tmux kill-session -t pubmed_eval"
echo "  tmux kill-session -t finqa_eval"

# Function to monitor progress
monitor_progress() {
    echo ""
    echo "🔍 Monitoring evaluation progress..."
    echo "Press Ctrl+C to stop monitoring"
    echo ""
    
    while true; do
        clear
        echo "=============================================="
        echo "EVALUATION PROGRESS MONITOR"
        echo "=============================================="
        echo "$(date)"
        echo ""
        
        # Check session status
        echo "📊 Session Status:"
        for session in casehold_eval pubmed_eval finqa_eval; do
            if session_exists "$session"; then
                echo "  ✅ $session: Running"
            else
                echo "  ❌ $session: Not running"
            fi
        done
        
        echo ""
        echo "📈 Latest Metrics (last line from each CSV):"
        
        # Show latest metrics from each dataset
        for dataset in casehold pubmed finqa; do
            csv_file="benchmark/$dataset/student_metrics.csv"
            if [ -f "$csv_file" ]; then
                echo "  📋 $dataset:"
                tail -n 1 "$csv_file" | cut -d',' -f1-6 | sed 's/^/    /'
            else
                echo "  📋 $dataset: No metrics file yet"
            fi
        done
        
        echo ""
        echo "Press Ctrl+C to exit monitor"
        sleep 10
    done
}

# Ask if user wants to monitor progress
echo ""
read -p "Monitor progress in real-time? (y/n): " monitor_choice
if [[ $monitor_choice =~ ^[Yy]$ ]]; then
    monitor_progress
fi

echo ""
echo "🎉 All evaluations have been started!"
echo "Use the monitoring commands above to check progress."
