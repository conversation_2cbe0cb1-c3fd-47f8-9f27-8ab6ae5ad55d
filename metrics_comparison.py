#!/usr/bin/env python3
"""
Metrics Comparison Tool
Compares teacher and student model performance across benchmark datasets.
"""

import os
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional
import argparse
from datetime import datetime

class MetricsComparator:
    def __init__(self, teacher_metrics_dir: str = "./benchmark", student_metrics_dir: str = "./student_metrics"):
        self.teacher_metrics_dir = teacher_metrics_dir
        self.student_metrics_dir = student_metrics_dir
        self.datasets = ['casehold', 'pubmed', 'finqa']
        
    def load_teacher_metrics(self) -> Dict[str, Dict[str, float]]:
        """Load teacher metrics from benchmark directories"""
        teacher_metrics = {}
        
        for dataset in self.datasets:
            dataset_path = os.path.join(self.teacher_metrics_dir, dataset)
            
            # Look for teacher metrics files
            metrics_files = []
            if os.path.exists(dataset_path):
                for file in os.listdir(dataset_path):
                    if 'teacher' in file.lower() and file.endswith('.csv'):
                        metrics_files.append(os.path.join(dataset_path, file))
                    elif 'metrics_log.csv' in file:
                        metrics_files.append(os.path.join(dataset_path, file))
            
            if metrics_files:
                # Use the most recent metrics file
                latest_file = max(metrics_files, key=os.path.getmtime)
                try:
                    df = pd.read_csv(latest_file)
                    if not df.empty:
                        # Get the last row (most recent metrics)
                        last_row = df.iloc[-1]
                        teacher_metrics[dataset] = {
                            'accuracy': float(last_row.get('Accuracy', 0)),
                            'precision': float(last_row.get('Precision', 0)),
                            'recall': float(last_row.get('Recall', 0)),
                            'f1_score': float(last_row.get('F1 Score', 0)),
                            'f1_macro': float(last_row.get('Macro F1 Score', last_row.get('F1 Score', 0))),
                            'invalid_cases': int(last_row.get('Invalid Cases', 0))
                        }
                except Exception as e:
                    print(f"Error loading teacher metrics for {dataset}: {e}")
                    teacher_metrics[dataset] = self._get_default_metrics()
            else:
                print(f"No teacher metrics found for {dataset}")
                teacher_metrics[dataset] = self._get_default_metrics()
        
        return teacher_metrics
    
    def load_student_metrics(self) -> Dict[str, Dict[str, float]]:
        """Load student metrics from student metrics directory"""
        student_metrics = {}
        
        for dataset in self.datasets:
            json_file = os.path.join(self.student_metrics_dir, f"{dataset}_student_metrics.json")
            csv_file = os.path.join(self.student_metrics_dir, f"{dataset}_student_metrics.csv")
            
            if os.path.exists(json_file):
                try:
                    with open(json_file, 'r') as f:
                        data = json.load(f)
                        student_metrics[dataset] = {
                            'accuracy': float(data.get('accuracy', 0)),
                            'precision': float(data.get('precision', 0)),
                            'recall': float(data.get('recall', 0)),
                            'f1_score': float(data.get('f1_score', 0)),
                            'f1_macro': float(data.get('f1_macro', 0)),
                            'invalid_cases': int(data.get('invalid_cases', 0)),
                            'total_cases': int(data.get('total_cases', 0)),
                            'valid_cases': int(data.get('valid_cases', 0))
                        }
                except Exception as e:
                    print(f"Error loading student metrics for {dataset}: {e}")
                    student_metrics[dataset] = self._get_default_metrics()
            elif os.path.exists(csv_file):
                try:
                    df = pd.read_csv(csv_file)
                    if not df.empty:
                        last_row = df.iloc[-1]
                        student_metrics[dataset] = {
                            'accuracy': float(last_row.get('accuracy', 0)),
                            'precision': float(last_row.get('precision', 0)),
                            'recall': float(last_row.get('recall', 0)),
                            'f1_score': float(last_row.get('f1_score', 0)),
                            'f1_macro': float(last_row.get('f1_macro', 0)),
                            'invalid_cases': int(last_row.get('invalid_cases', 0)),
                            'total_cases': int(last_row.get('total_cases', 0)),
                            'valid_cases': int(last_row.get('valid_cases', 0))
                        }
                except Exception as e:
                    print(f"Error loading student metrics for {dataset}: {e}")
                    student_metrics[dataset] = self._get_default_metrics()
            else:
                print(f"No student metrics found for {dataset}")
                student_metrics[dataset] = self._get_default_metrics()
        
        return student_metrics
    
    def _get_default_metrics(self) -> Dict[str, float]:
        """Return default metrics structure"""
        return {
            'accuracy': 0.0,
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'f1_macro': 0.0,
            'invalid_cases': 0,
            'total_cases': 0,
            'valid_cases': 0
        }
    
    def compare_metrics(self, teacher_metrics: Dict, student_metrics: Dict) -> Dict[str, Dict[str, float]]:
        """Compare teacher and student metrics"""
        comparison = {}
        
        for dataset in self.datasets:
            teacher_data = teacher_metrics.get(dataset, self._get_default_metrics())
            student_data = student_metrics.get(dataset, self._get_default_metrics())
            
            comparison[dataset] = {
                'teacher_accuracy': teacher_data['accuracy'],
                'student_accuracy': student_data['accuracy'],
                'accuracy_diff': student_data['accuracy'] - teacher_data['accuracy'],
                'accuracy_ratio': (student_data['accuracy'] / teacher_data['accuracy']) if teacher_data['accuracy'] > 0 else 0,
                
                'teacher_f1': teacher_data['f1_score'],
                'student_f1': student_data['f1_score'],
                'f1_diff': student_data['f1_score'] - teacher_data['f1_score'],
                'f1_ratio': (student_data['f1_score'] / teacher_data['f1_score']) if teacher_data['f1_score'] > 0 else 0,
                
                'teacher_precision': teacher_data['precision'],
                'student_precision': student_data['precision'],
                'precision_diff': student_data['precision'] - teacher_data['precision'],
                
                'teacher_recall': teacher_data['recall'],
                'student_recall': student_data['recall'],
                'recall_diff': student_data['recall'] - teacher_data['recall'],
                
                'teacher_invalid': teacher_data['invalid_cases'],
                'student_invalid': student_data['invalid_cases'],
                'student_total': student_data.get('total_cases', 0),
                'student_valid': student_data.get('valid_cases', 0)
            }
        
        return comparison
    
    def create_comparison_report(self, comparison: Dict, output_dir: str = "./comparison_results"):
        """Create detailed comparison report"""
        os.makedirs(output_dir, exist_ok=True)
        
        # Create summary table
        summary_data = []
        for dataset, metrics in comparison.items():
            summary_data.append({
                'Dataset': dataset.upper(),
                'Teacher Accuracy (%)': f"{metrics['teacher_accuracy']:.2f}",
                'Student Accuracy (%)': f"{metrics['student_accuracy']:.2f}",
                'Accuracy Difference': f"{metrics['accuracy_diff']:+.2f}",
                'Accuracy Retention (%)': f"{metrics['accuracy_ratio']*100:.1f}",
                'Teacher F1 (%)': f"{metrics['teacher_f1']:.2f}",
                'Student F1 (%)': f"{metrics['student_f1']:.2f}",
                'F1 Difference': f"{metrics['f1_diff']:+.2f}",
                'F1 Retention (%)': f"{metrics['f1_ratio']*100:.1f}",
                'Student Valid Cases': f"{metrics['student_valid']}/{metrics['student_total']}"
            })
        
        summary_df = pd.DataFrame(summary_data)
        
        # Save summary
        summary_path = os.path.join(output_dir, "teacher_student_comparison.csv")
        summary_df.to_csv(summary_path, index=False)
        
        # Save detailed comparison
        detailed_path = os.path.join(output_dir, "detailed_comparison.json")
        with open(detailed_path, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'comparison': comparison,
                'summary': summary_data
            }, f, indent=2)
        
        print(f"Comparison report saved to {summary_path}")
        print(f"Detailed comparison saved to {detailed_path}")
        
        return summary_df
    
    def create_visualizations(self, comparison: Dict, output_dir: str = "./comparison_results"):
        """Create visualization plots"""
        os.makedirs(output_dir, exist_ok=True)
        
        # Set up the plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Create comparison plots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Teacher vs Student Model Performance Comparison', fontsize=16, fontweight='bold')
        
        datasets = list(comparison.keys())
        
        # Accuracy comparison
        teacher_acc = [comparison[d]['teacher_accuracy'] for d in datasets]
        student_acc = [comparison[d]['student_accuracy'] for d in datasets]
        
        x = range(len(datasets))
        width = 0.35
        
        axes[0, 0].bar([i - width/2 for i in x], teacher_acc, width, label='Teacher', alpha=0.8)
        axes[0, 0].bar([i + width/2 for i in x], student_acc, width, label='Student', alpha=0.8)
        axes[0, 0].set_xlabel('Dataset')
        axes[0, 0].set_ylabel('Accuracy (%)')
        axes[0, 0].set_title('Accuracy Comparison')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels([d.upper() for d in datasets])
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # F1 Score comparison
        teacher_f1 = [comparison[d]['teacher_f1'] for d in datasets]
        student_f1 = [comparison[d]['student_f1'] for d in datasets]
        
        axes[0, 1].bar([i - width/2 for i in x], teacher_f1, width, label='Teacher', alpha=0.8)
        axes[0, 1].bar([i + width/2 for i in x], student_f1, width, label='Student', alpha=0.8)
        axes[0, 1].set_xlabel('Dataset')
        axes[0, 1].set_ylabel('F1 Score (%)')
        axes[0, 1].set_title('F1 Score Comparison')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels([d.upper() for d in datasets])
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # Performance retention
        acc_retention = [comparison[d]['accuracy_ratio']*100 for d in datasets]
        f1_retention = [comparison[d]['f1_ratio']*100 for d in datasets]
        
        axes[1, 0].bar([i - width/2 for i in x], acc_retention, width, label='Accuracy Retention', alpha=0.8)
        axes[1, 0].bar([i + width/2 for i in x], f1_retention, width, label='F1 Retention', alpha=0.8)
        axes[1, 0].set_xlabel('Dataset')
        axes[1, 0].set_ylabel('Retention (%)')
        axes[1, 0].set_title('Performance Retention (Student/Teacher)')
        axes[1, 0].set_xticks(x)
        axes[1, 0].set_xticklabels([d.upper() for d in datasets])
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].axhline(y=100, color='red', linestyle='--', alpha=0.7, label='100% Retention')
        
        # Performance differences
        acc_diff = [comparison[d]['accuracy_diff'] for d in datasets]
        f1_diff = [comparison[d]['f1_diff'] for d in datasets]
        
        axes[1, 1].bar([i - width/2 for i in x], acc_diff, width, label='Accuracy Diff', alpha=0.8)
        axes[1, 1].bar([i + width/2 for i in x], f1_diff, width, label='F1 Diff', alpha=0.8)
        axes[1, 1].set_xlabel('Dataset')
        axes[1, 1].set_ylabel('Difference (Student - Teacher)')
        axes[1, 1].set_title('Performance Differences')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels([d.upper() for d in datasets])
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].axhline(y=0, color='red', linestyle='-', alpha=0.7)
        
        plt.tight_layout()
        
        # Save plot
        plot_path = os.path.join(output_dir, "performance_comparison.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Visualization saved to {plot_path}")
    
    def run_comparison(self, output_dir: str = "./comparison_results"):
        """Run complete comparison analysis"""
        print("Loading teacher metrics...")
        teacher_metrics = self.load_teacher_metrics()
        
        print("Loading student metrics...")
        student_metrics = self.load_student_metrics()
        
        print("Comparing metrics...")
        comparison = self.compare_metrics(teacher_metrics, student_metrics)
        
        print("Creating comparison report...")
        summary_df = self.create_comparison_report(comparison, output_dir)
        
        print("Creating visualizations...")
        self.create_visualizations(comparison, output_dir)
        
        # Print summary to console
        print("\n" + "="*80)
        print("TEACHER vs STUDENT PERFORMANCE COMPARISON")
        print("="*80)
        print(summary_df.to_string(index=False))
        print("="*80)
        
        return comparison, summary_df

def main():
    parser = argparse.ArgumentParser(description='Compare teacher and student model performance')
    parser.add_argument('--teacher-dir', default='./benchmark', help='Directory containing teacher metrics')
    parser.add_argument('--student-dir', default='./student_metrics', help='Directory containing student metrics')
    parser.add_argument('--output-dir', default='./comparison_results', help='Output directory for comparison results')
    
    args = parser.parse_args()
    
    comparator = MetricsComparator(args.teacher_dir, args.student_dir)
    comparison, summary = comparator.run_comparison(args.output_dir)
    
    print(f"\nComparison completed! Results saved to {args.output_dir}")

if __name__ == "__main__":
    main()
