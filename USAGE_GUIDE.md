# Clean Evaluation System - Usage Guide

This guide shows how to use the modularized evaluation system to run student model inference on benchmark datasets and compare performance.

## 🚀 Quick Start

### 1. Run Student Model Evaluation

#### Evaluate on a single dataset:
```bash
# Evaluate on CaseHOLD dataset
python run_evaluation.py --model ./distilled_student/final_student_model --dataset casehold

# Evaluate on PubMedQA dataset  
python run_evaluation.py --model ./distilled_student/final_student_model --dataset pubmed

# Evaluate on FinQA dataset
python run_evaluation.py --model ./distilled_student/final_student_model --dataset finqa
```

#### Evaluate on all datasets:
```bash
python run_evaluation.py --model ./distilled_student/final_student_model --all-datasets
```

#### Use common model names:
```bash
# Use predefined model names
python run_evaluation.py --model llama2-1b --all-datasets
python run_evaluation.py --model llama3.2-1b --dataset casehold
```

### 2. Compare Performance

#### Compare teacher vs student:
```bash
python compare_performance.py --teacher-vs-student final_student_model
```

#### Compare multiple student models:
```bash
python compare_performance.py --compare-students model1 model2 model3
```

#### Show available results:
```bash
python compare_performance.py --show-results
```

## 📁 File Structure

```
./
├── evaluators.py              # Dataset evaluators (CaseHOLD, PubMedQA, FinQA)
├── model_manager.py           # Model loading and management
├── metrics_manager.py         # Metrics storage and comparison
├── run_evaluation.py          # Main evaluation runner
├── compare_performance.py     # Performance comparison tool
└── metrics/                   # Generated metrics and reports
    ├── model_dataset_metrics.json
    ├── summary_report.txt
    ├── accuracy_comparison.png
    └── all_metrics.csv
```

## 🔧 Detailed Usage

### Model Evaluation

The `run_evaluation.py` script provides a clean interface for running evaluations:

```bash
# Basic usage
python run_evaluation.py --model MODEL_PATH --dataset DATASET_NAME

# Options
--model MODEL_PATH          # Path to model or model name
--dataset {casehold,pubmed,finqa}  # Dataset to evaluate
--all-datasets             # Evaluate on all datasets
--dataset-size SIZE         # Number of examples (default: 1000)
--device {auto,cuda,cpu}    # Device to use
--list-models              # Show available models
```

### Performance Comparison

The `compare_performance.py` script handles all comparison tasks:

```bash
# Teacher vs Student comparison
python compare_performance.py --teacher-vs-student STUDENT_MODEL

# Multi-model comparison
python compare_performance.py --compare-students MODEL1 MODEL2 MODEL3

# Options
--benchmark-dir DIR         # Teacher benchmark directory (default: ./benchmark)
--show-results             # List available evaluation results
```

## 📊 Output Examples

### Single Dataset Evaluation
```
==============================================================
EVALUATING CASEHOLD DATASET
==============================================================
Loading model...
✅ Model loaded successfully!
   Parameters: 1,100,048,896
   Vocab size: 32,000

Evaluating casehold dataset (size: 1000)
Processing example 1/1000
Processing example 101/1000
...

📊 RESULTS FOR CASEHOLD:
   Accuracy:     78.50%
   Precision:    78.23%
   Recall:       78.50%
   F1 Score:     78.36%
   Macro F1:     77.89%
   Valid Cases:  785/1000
   Invalid Cases: 215

Metrics saved to: ./metrics/final_student_model_casehold_metrics.json
✅ Evaluation completed successfully!
```

### Teacher vs Student Comparison
```
📊 TEACHER vs STUDENT COMPARISON
Student Model: final_student_model
============================================================

Dataset    Teacher Accuracy (%)  Student Accuracy (%)  Accuracy Retention (%)  Teacher F1 (%)  Student F1 (%)  F1 Retention (%)  Student Valid/Total
CASEHOLD   85.20                 78.50                 92.1                    84.80           78.36           92.4              785/1000
PUBMED     82.30                 76.80                 93.3                    81.90           76.45           93.3              768/1000
FINQA      79.40                 71.20                 89.7                    78.60           70.85           90.1              712/1000

📈 SUMMARY
------------------------------
Average Teacher Accuracy: 82.30%
Average Student Accuracy: 75.50%
Average Accuracy Retention: 91.7%
Average Teacher F1: 81.77%
Average Student F1: 75.22%
Average F1 Retention: 91.9%

🎯 PERFORMANCE ASSESSMENT
------------------------------
🟢 Excellent retention (≥90%)

💾 Comparison saved to: ./metrics/teacher_vs_final_student_model_comparison.csv
```

## 🎯 Key Features

### Modular Design
- **Evaluators**: Clean, extensible evaluators for each dataset
- **Model Manager**: Unified model loading with device management
- **Metrics Manager**: Centralized metrics storage and comparison

### Easy Dataset Addition
To add a new dataset, simply:
1. Create a new evaluator class in `evaluators.py`
2. Implement the required methods
3. Add to the evaluator factory

### Flexible Model Support
- Local model paths
- HuggingFace model names
- Predefined common models
- Automatic device detection

### Comprehensive Metrics
- Accuracy, Precision, Recall, F1 scores
- Performance retention percentages
- Visual comparison charts
- Detailed summary reports

## 🔍 Advanced Usage

### Custom Dataset Size
```bash
# Evaluate on smaller subset for quick testing
python run_evaluation.py --model my_model --dataset casehold --dataset-size 100

# Full evaluation
python run_evaluation.py --model my_model --all-datasets --dataset-size 5000
```

### Device Selection
```bash
# Force CPU usage
python run_evaluation.py --model my_model --dataset casehold --device cpu

# Force specific GPU
python run_evaluation.py --model my_model --dataset casehold --device cuda:1
```

### Batch Evaluation
```bash
# Evaluate multiple models on all datasets
for model in model1 model2 model3; do
    python run_evaluation.py --model $model --all-datasets
done

# Compare all models
python compare_performance.py --compare-students model1 model2 model3
```

## 📈 Metrics Files

All metrics are saved in JSON format for easy processing:

```json
{
  "model_name": "final_student_model",
  "dataset": "casehold",
  "timestamp": "2024-01-15T10:30:00",
  "accuracy": 78.50,
  "precision": 78.23,
  "recall": 78.50,
  "f1_score": 78.36,
  "f1_macro": 77.89,
  "correct_predictions": 785,
  "total_valid": 785,
  "invalid_cases": 215,
  "total_cases": 1000
}
```

## 🛠️ Troubleshooting

### Model Loading Issues
```bash
# List available models
python run_evaluation.py --list-models

# Check model path
ls -la ./distilled_student/final_student_model/
```

### Memory Issues
```bash
# Use CPU if GPU memory is insufficient
python run_evaluation.py --model my_model --dataset casehold --device cpu

# Reduce dataset size
python run_evaluation.py --model my_model --dataset casehold --dataset-size 100
```

### Missing Dependencies
```bash
pip install torch transformers datasets scikit-learn pandas matplotlib seaborn
```

This modular system makes it easy to evaluate student models on individual datasets and compare performance across different models and against teacher baselines.
