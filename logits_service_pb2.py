# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: logits_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'logits_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14logits_service.proto\x12\x06logits\"P\n\rLogitsRequest\x12\x11\n\tinput_ids\x18\x01 \x01(\t\x12\x16\n\x0e\x61ttention_mask\x18\x02 \x01(\t\x12\x14\n\x0cmodel_config\x18\x03 \x01(\t\"H\n\x0eLogitsResponse\x12\x0e\n\x06logits\x18\x01 \x01(\t\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t2K\n\rLogitsService\x12:\n\tGetLogits\x12\x15.logits.LogitsRequest\x1a\x16.logits.LogitsResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'logits_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_LOGITSREQUEST']._serialized_start=32
  _globals['_LOGITSREQUEST']._serialized_end=112
  _globals['_LOGITSRESPONSE']._serialized_start=114
  _globals['_LOGITSRESPONSE']._serialized_end=186
  _globals['_LOGITSSERVICE']._serialized_start=188
  _globals['_LOGITSSERVICE']._serialized_end=263
# @@protoc_insertion_point(module_scope)
