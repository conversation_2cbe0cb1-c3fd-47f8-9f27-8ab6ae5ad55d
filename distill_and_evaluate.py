import os
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, TrainingArguments
from datasets import load_dataset
from distributed_teacher_client import DistributedTeacherClient
from student_trainer import DistributedLogitsTrainer
import json

# List of teacher server addresses (update with your actual server IPs/ports)
TEACHER_SERVERS = [
    "***************:50051",
    "***************:50051",
]

STUDENT_MODEL = "meta-llama/llama-2-1b"
MAX_LENGTH = 4096
OUTPUT_DIR = "./distilled_student"

DATASETS = [
    {"name": "casehold", "path": "benchmark/casehold", "file": "metrics_log.csv"},
    {"name": "finqa", "path": "benchmark/finqa", "file": "teacher_metrics.csv"},
    {"name": "pubmed", "path": "benchmark/pubmed", "file": "metrics_log.csv"}
]

TRAINING_ARGS = {
    "output_dir": OUTPUT_DIR,
    "num_train_epochs": 3,
    "per_device_train_batch_size": 1,
    "gradient_accumulation_steps": 8,
    "save_steps": 1000,
    "logging_first_step": True,
    "logging_steps": 1,
    "learning_rate": 2e-5,
    "weight_decay": 0.05,
    "warmup_ratio": 0.1,
    "lr_scheduler_type": "cosine",
    "bf16": True,
    "disable_tqdm": False,
}

DISTILLATION_ARGS = {"temperature": 2.0, "alpha": 0.5}


def preprocess_dataset(dataset, tokenizer):
    def format_example(example):
        # This should be adapted to your dataset format
        if "conversations" in example:
            conversations = example["conversations"]
            message = []
            for conversation in conversations:
                if conversation.get('from') == 'human':
                    message.append({"role": "user", "content": conversation.get('value', '')})
                elif conversation.get('from') == 'gpt':
                    message.append({"role": "assistant", "content": conversation.get('value', '')})
                elif conversation.get('from') == 'system':
                    message.insert(0, {"role": "system", "content": conversation.get('value', '')})
            if not any(msg.get('role') == 'system' for msg in message):
                message.insert(0, {"role": "system", "content": "You are a helpful assistant."})
            text = tokenizer.apply_chat_template(message, tokenize=False, add_generation_prompt=True)
            return {"text": text}
        elif "text" in example:
            return {"text": example["text"]}
        else:
            return {"text": str(example)}
    original_columns = dataset.column_names
    dataset = dataset.map(format_example, remove_columns=original_columns)
    def tokenize_function(examples):
        return tokenizer(examples["text"], truncation=True, max_length=MAX_LENGTH, padding="max_length")
    tokenized_dataset = dataset.map(tokenize_function, batched=True, num_proc=4, remove_columns=["text"])
    return tokenized_dataset.train_test_split(test_size=0.1)


def train_and_evaluate_on_dataset(dataset_name, dataset_path, teacher_client, tokenizer, student_model):
    # Load dataset (adapt as needed for your data format)
    if os.path.isdir(dataset_path):
        # Try to load HuggingFace dataset if available, else fallback to CSV
        try:
            dataset = load_dataset(dataset_path)
        except Exception:
            csv_files = [f for f in os.listdir(dataset_path) if f.endswith('.csv') or f.endswith('.json')]
            if not csv_files:
                raise FileNotFoundError(f"No CSV/JSON found in {dataset_path}")
            dataset = load_dataset('csv', data_files=os.path.join(dataset_path, csv_files[0]))
    else:
        dataset = load_dataset('csv', data_files=dataset_path)
    dataset = dataset["train"] if "train" in dataset else dataset[list(dataset.keys())[0]]
    tokenized = preprocess_dataset(dataset, tokenizer)
    training_args = TrainingArguments(**TRAINING_ARGS, output_dir=f"{OUTPUT_DIR}/{dataset_name}")
    trainer = DistributedLogitsTrainer(
        teacher_client=teacher_client,
        temperature=DISTILLATION_ARGS["temperature"],
        alpha=DISTILLATION_ARGS["alpha"],
        model=student_model,
        train_dataset=tokenized["train"],
        eval_dataset=tokenized["test"],
        args=training_args,
    )
    trainer.train()
    trainer.save_model(f"{OUTPUT_DIR}/{dataset_name}")
    # Evaluate
    eval_metrics = trainer.evaluate()
    with open(f"{OUTPUT_DIR}/{dataset_name}/eval_metrics.json", "w") as f:
        json.dump(eval_metrics, f, indent=2)
    print(f"Finished distillation and evaluation for {dataset_name}")


def main():
    teacher_client = DistributedTeacherClient(TEACHER_SERVERS)
    student_model = AutoModelForCausalLM.from_pretrained(STUDENT_MODEL, torch_dtype=torch.bfloat16)
    tokenizer = AutoTokenizer.from_pretrained(STUDENT_MODEL)
    if not tokenizer.pad_token:
        tokenizer.pad_token = tokenizer.eos_token
    for dataset in DATASETS:
        print(f"\n--- Processing {dataset['name']} ---")
        train_and_evaluate_on_dataset(dataset["name"], dataset["path"], teacher_client, tokenizer, student_model)
    teacher_client.close()
    print("\nAll datasets processed. Distilled models and metrics saved.")

if __name__ == "__main__":
    main()
