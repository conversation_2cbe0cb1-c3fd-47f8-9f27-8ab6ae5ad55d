import os
import torch
import subprocess
import sys
from transformers import AutoModelForCausalLM, AutoTokenizer, TrainingArguments
from datasets import load_dataset
from distributed_teacher_client import DistributedTeacherClient
from student_trainer import DistributedLogitsTrainer
import json
from datetime import datetime
import argparse

# Configuration
DEFAULT_CONFIG = {
    "teacher_servers": [
        "localhost:50051",  # Update with your actual server addresses
        # "***************:50051",
        # "***************:50051",
    ],
    "teacher_model": "meta-llama/Llama-2-13b-hf",
    "student_model": "meta-llama/Llama-2-1b-hf",
    "max_length": 4096,
    "output_dir": "./distilled_student",
    "datasets": [
        {"name": "casehold", "path": "benchmark/casehold", "file": "metrics_log.csv"},
        {"name": "finqa", "path": "benchmark/finqa", "file": "teacher_metrics.csv"},
        {"name": "pubmed", "path": "benchmark/pubmed", "file": "metrics_log.csv"}
    ],
    "training_args": {
        "num_train_epochs": 3,
        "per_device_train_batch_size": 1,
        "gradient_accumulation_steps": 8,
        "save_steps": 1000,
        "logging_first_step": True,
        "logging_steps": 10,
        "learning_rate": 2e-5,
        "weight_decay": 0.05,
        "warmup_ratio": 0.1,
        "lr_scheduler_type": "cosine",
        "bf16": True,
        "disable_tqdm": False,
        "evaluation_strategy": "steps",
        "eval_steps": 500,
        "save_total_limit": 3,
        "load_best_model_at_end": True,
        "metric_for_best_model": "eval_loss",
        "greater_is_better": False,
    },
    "distillation_args": {
        "temperature": 2.0,
        "alpha": 0.5,
        "vocab_sample_size": 5000,
        "vocab_sample_strategy": "top_k"
    }
}


def create_distillation_dataset(config):
    """Create a general distillation dataset from multiple sources"""
    print("Creating distillation dataset...")

    # For this implementation, we'll use a general instruction-following dataset
    # You can modify this to use your specific datasets
    try:
        # Try to load a general instruction dataset
        dataset = load_dataset("mlabonne/FineTome-100k", split="train")
        dataset = dataset.select(range(10000))  # Use subset for faster training
        print(f"Loaded {len(dataset)} examples from FineTome-100k")
    except Exception as e:
        print(f"Failed to load FineTome-100k: {e}")
        # Fallback to a smaller dataset
        try:
            dataset = load_dataset("tatsu-lab/alpaca", split="train")
            dataset = dataset.select(range(5000))
            print(f"Loaded {len(dataset)} examples from Alpaca")
        except Exception as e2:
            print(f"Failed to load Alpaca: {e2}")
            raise Exception("Could not load any training dataset")

    return dataset

def preprocess_dataset(dataset, tokenizer, max_length=4096):
    """Preprocess dataset for distillation training"""
    def format_example(example):
        # Handle different dataset formats
        if "conversations" in example:
            conversations = example["conversations"]
            message = []
            for conversation in conversations:
                if conversation.get('from') == 'human':
                    message.append({"role": "user", "content": conversation.get('value', '')})
                elif conversation.get('from') == 'gpt':
                    message.append({"role": "assistant", "content": conversation.get('value', '')})
                elif conversation.get('from') == 'system':
                    message.insert(0, {"role": "system", "content": conversation.get('value', '')})
            if not any(msg.get('role') == 'system' for msg in message):
                message.insert(0, {"role": "system", "content": "You are a helpful assistant."})
            text = tokenizer.apply_chat_template(message, tokenize=False, add_generation_prompt=True)
            return {"text": text}
        elif "instruction" in example and "output" in example:
            # Alpaca format
            instruction = example["instruction"]
            input_text = example.get("input", "")
            output = example["output"]

            if input_text:
                prompt = f"### Instruction:\n{instruction}\n\n### Input:\n{input_text}\n\n### Response:\n{output}"
            else:
                prompt = f"### Instruction:\n{instruction}\n\n### Response:\n{output}"
            return {"text": prompt}
        elif "text" in example:
            return {"text": example["text"]}
        else:
            return {"text": str(example)}

    original_columns = dataset.column_names
    dataset = dataset.map(format_example, remove_columns=original_columns)

    def tokenize_function(examples):
        return tokenizer(examples["text"], truncation=True, max_length=max_length, padding="max_length")

    tokenized_dataset = dataset.map(tokenize_function, batched=True, num_proc=4, remove_columns=["text"])
    return tokenized_dataset.train_test_split(test_size=0.1)


def train_and_evaluate_on_dataset(dataset_name, dataset_path, teacher_client, tokenizer, student_model):
    # Load dataset (adapt as needed for your data format)
    if os.path.isdir(dataset_path):
        # Try to load HuggingFace dataset if available, else fallback to CSV
        try:
            dataset = load_dataset(dataset_path)
        except Exception:
            csv_files = [f for f in os.listdir(dataset_path) if f.endswith('.csv') or f.endswith('.json')]
            if not csv_files:
                raise FileNotFoundError(f"No CSV/JSON found in {dataset_path}")
            dataset = load_dataset('csv', data_files=os.path.join(dataset_path, csv_files[0]))
    else:
        dataset = load_dataset('csv', data_files=dataset_path)
    dataset = dataset["train"] if "train" in dataset else dataset[list(dataset.keys())[0]]
    tokenized = preprocess_dataset(dataset, tokenizer)
    training_args = TrainingArguments(**TRAINING_ARGS, output_dir=f"{OUTPUT_DIR}/{dataset_name}")
    trainer = DistributedLogitsTrainer(
        teacher_client=teacher_client,
        temperature=DISTILLATION_ARGS["temperature"],
        alpha=DISTILLATION_ARGS["alpha"],
        model=student_model,
        train_dataset=tokenized["train"],
        eval_dataset=tokenized["test"],
        args=training_args,
    )
    trainer.train()
    trainer.save_model(f"{OUTPUT_DIR}/{dataset_name}")
    # Evaluate
    eval_metrics = trainer.evaluate()
    with open(f"{OUTPUT_DIR}/{dataset_name}/eval_metrics.json", "w") as f:
        json.dump(eval_metrics, f, indent=2)
    print(f"Finished distillation and evaluation for {dataset_name}")


def main():
    teacher_client = DistributedTeacherClient(TEACHER_SERVERS)
    student_model = AutoModelForCausalLM.from_pretrained(STUDENT_MODEL, torch_dtype=torch.bfloat16)
    tokenizer = AutoTokenizer.from_pretrained(STUDENT_MODEL)
    if not tokenizer.pad_token:
        tokenizer.pad_token = tokenizer.eos_token
    for dataset in DATASETS:
        print(f"\n--- Processing {dataset['name']} ---")
        train_and_evaluate_on_dataset(dataset["name"], dataset["path"], teacher_client, tokenizer, student_model)
    teacher_client.close()
    print("\nAll datasets processed. Distilled models and metrics saved.")

if __name__ == "__main__":
    main()
