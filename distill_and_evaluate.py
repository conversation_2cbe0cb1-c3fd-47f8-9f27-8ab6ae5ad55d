import os
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, TrainingArguments
from datasets import load_dataset
from distributed_teacher_client import DistributedTeacherClient
from student_trainer import DistributedLogitsTrainer
import json

# List of teacher server addresses (update with your actual server IPs/ports)
TEACHER_SERVERS = [
    "***************:50051",
    "***************:50051",
]

STUDENT_MODEL = "meta-llama/Llama-2-1b-hf"
MAX_LENGTH = 4096
OUTPUT_DIR = "./distilled_student"

DATASETS = [
    {"name": "casehold", "path": "benchmark/casehold", "file": "metrics_log.csv"},
    {"name": "finqa", "path": "benchmark/finqa", "file": "teacher_metrics.csv"},
    {"name": "pubmed", "path": "benchmark/pubmed", "file": "metrics_log.csv"}
]

TRAINING_ARGS = {
    "output_dir": OUTPUT_DIR,
    "num_train_epochs": 3,
    "per_device_train_batch_size": 1,
    "gradient_accumulation_steps": 8,
    "save_steps": 1000,
    "logging_first_step": True,
    "logging_steps": 1,
    "learning_rate": 2e-5,
    "weight_decay": 0.05,
    "warmup_ratio": 0.1,
    "lr_scheduler_type": "cosine",
    "bf16": True,
    "disable_tqdm": False,
}

DISTILLATION_ARGS = {"temperature": 2.0, "alpha": 0.5}


def create_distillation_dataset(config):
    """Create a general distillation dataset from multiple sources"""
    print("Creating distillation dataset...")

    # For this implementation, we'll use a general instruction-following dataset
    # You can modify this to use your specific datasets
    try:
        # Try to load a general instruction dataset
        dataset = load_dataset("mlabonne/FineTome-100k", split="train")
        dataset = dataset.select(range(10000))  # Use subset for faster training
        print(f"Loaded {len(dataset)} examples from FineTome-100k")
    except Exception as e:
        print(f"Failed to load FineTome-100k: {e}")
        # Fallback to a smaller dataset
        try:
            dataset = load_dataset("tatsu-lab/alpaca", split="train")
            dataset = dataset.select(range(5000))
            print(f"Loaded {len(dataset)} examples from Alpaca")
        except Exception as e2:
            print(f"Failed to load Alpaca: {e2}")
            raise Exception("Could not load any training dataset")

    return dataset

def preprocess_dataset(dataset, tokenizer, max_length=4096):
    """Preprocess dataset for distillation training"""
    def format_example(example):
        # Handle different dataset formats
        if "conversations" in example:
            conversations = example["conversations"]
            message = []
            for conversation in conversations:
                if conversation.get('from') == 'human':
                    message.append({"role": "user", "content": conversation.get('value', '')})
                elif conversation.get('from') == 'gpt':
                    message.append({"role": "assistant", "content": conversation.get('value', '')})
                elif conversation.get('from') == 'system':
                    message.insert(0, {"role": "system", "content": conversation.get('value', '')})
            if not any(msg.get('role') == 'system' for msg in message):
                message.insert(0, {"role": "system", "content": "You are a helpful assistant."})
            text = tokenizer.apply_chat_template(message, tokenize=False, add_generation_prompt=True)
            return {"text": text}
        elif "instruction" in example and "output" in example:
            # Alpaca format
            instruction = example["instruction"]
            input_text = example.get("input", "")
            output = example["output"]

            if input_text:
                prompt = f"### Instruction:\n{instruction}\n\n### Input:\n{input_text}\n\n### Response:\n{output}"
            else:
                prompt = f"### Instruction:\n{instruction}\n\n### Response:\n{output}"
            return {"text": prompt}
        elif "text" in example:
            return {"text": example["text"]}
        else:
            return {"text": str(example)}

    original_columns = dataset.column_names
    dataset = dataset.map(format_example, remove_columns=original_columns)

    def tokenize_function(examples):
        return tokenizer(examples["text"], truncation=True, max_length=max_length, padding="max_length")

    tokenized_dataset = dataset.map(tokenize_function, batched=True, num_proc=4, remove_columns=["text"])
    return tokenized_dataset.train_test_split(test_size=0.1)





def train_student_model():
    """Train the student model using distributed logits distillation"""
    print("Starting distributed logits distillation training...")

    # Initialize distributed teacher client
    teacher_client = DistributedTeacherClient(TEACHER_SERVERS)

    # Load student model and tokenizer
    student_model = AutoModelForCausalLM.from_pretrained(STUDENT_MODEL, torch_dtype=torch.bfloat16)
    tokenizer = AutoTokenizer.from_pretrained(STUDENT_MODEL)
    if not tokenizer.pad_token:
        tokenizer.pad_token = tokenizer.eos_token

    # Load training dataset (using a general instruction-following dataset)
    print("Loading training dataset...")
    try:
        dataset = load_dataset("mlabonne/FineTome-100k", split="train")
        dataset = dataset.select(range(10000))  # Use subset for faster training
        print(f"Loaded {len(dataset)} examples from FineTome-100k")
    except Exception as e:
        print(f"Failed to load FineTome-100k: {e}")
        # Fallback to Alpaca
        dataset = load_dataset("tatsu-lab/alpaca", split="train")
        dataset = dataset.select(range(5000))
        print(f"Loaded {len(dataset)} examples from Alpaca")

    # Preprocess dataset
    tokenized = preprocess_dataset(dataset, tokenizer)

    # Set up training arguments
    training_args = TrainingArguments(**TRAINING_ARGS, output_dir=OUTPUT_DIR)

    # Create distributed trainer
    trainer = DistributedLogitsTrainer(
        teacher_client=teacher_client,
        temperature=DISTILLATION_ARGS["temperature"],
        alpha=DISTILLATION_ARGS["alpha"],
        model=student_model,
        train_dataset=tokenized["train"],
        eval_dataset=tokenized["test"],
        args=training_args,
    )

    try:
        # Train the model
        print("Starting training...")
        trainer.train()

        # Save the final model
        final_model_path = f"{OUTPUT_DIR}/final_student_model"
        trainer.save_model(final_model_path)
        tokenizer.save_pretrained(final_model_path)

        print(f"Student model saved to {final_model_path}")

        # Evaluate on validation set
        eval_metrics = trainer.evaluate()
        with open(f"{final_model_path}/training_metrics.json", "w") as f:
            json.dump(eval_metrics, f, indent=2)

        return final_model_path

    finally:
        # Clean up
        teacher_client.close()

def run_student_inference(model_path: str):
    """Run student model inference on benchmark datasets"""
    print(f"\nRunning student model inference from {model_path}...")

    import subprocess
    import sys

    # Run student.py script
    cmd = [
        sys.executable, "student.py",
        "--model-path", model_path,
        "--datasets", "casehold", "pubmed", "finqa",
        "--dataset-size", "1000",
        "--output-dir", "./student_metrics"
    ]

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Student inference completed successfully!")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Error running student inference: {e}")
        print(f"Stdout: {e.stdout}")
        print(f"Stderr: {e.stderr}")

def compare_performance():
    """Compare teacher and student performance"""
    print("\nComparing teacher and student performance...")

    import subprocess
    import sys

    cmd = [
        sys.executable, "metrics_comparison.py",
        "--teacher-dir", "./benchmark",
        "--student-dir", "./student_metrics",
        "--output-dir", "./comparison_results"
    ]

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Performance comparison completed!")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Error running performance comparison: {e}")
        print(f"Stdout: {e.stdout}")
        print(f"Stderr: {e.stderr}")

def main():
    """Main pipeline: train student model, run inference, and compare performance"""
    print("="*80)
    print("DISTRIBUTED LOGITS DISTILLATION PIPELINE")
    print("="*80)

    # Step 1: Train student model with distributed logits distillation
    model_path = train_student_model()

    # Step 2: Run student model inference on benchmark datasets
    run_student_inference(model_path)

    # Step 3: Compare teacher and student performance
    compare_performance()

    print("\n" + "="*80)
    print("PIPELINE COMPLETED SUCCESSFULLY!")
    print("="*80)
    print(f"Student model saved to: {model_path}")
    print("Student metrics saved to: ./student_metrics")
    print("Comparison results saved to: ./comparison_results")

if __name__ == "__main__":
    main()
