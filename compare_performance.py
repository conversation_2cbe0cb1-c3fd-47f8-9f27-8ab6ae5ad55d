#!/usr/bin/env python3
"""
Performance Comparison Tool
Simple tool to compare teacher vs student model performance.
"""

import argparse
import pandas as pd
from metrics_manager import MetricsManager, load_teacher_metrics
from typing import List, Dict

def compare_teacher_student(student_model_name: str, benchmark_dir: str = "./benchmark") -> None:
    """
    Compare teacher and student model performance
    
    Args:
        student_model_name: Name of the student model
        benchmark_dir: Directory containing teacher benchmark results
    """
    print(f"\n📊 TEACHER vs STUDENT COMPARISON")
    print(f"Student Model: {student_model_name}")
    print("="*60)
    
    # Load teacher metrics
    print("Loading teacher metrics...")
    teacher_metrics = load_teacher_metrics(benchmark_dir)
    
    # Load student metrics
    print("Loading student metrics...")
    metrics_manager = MetricsManager()
    
    datasets = ['casehold', 'pubmed', 'finqa']
    comparison_data = []
    
    for dataset in datasets:
        student_metrics = metrics_manager.load_metrics(student_model_name, dataset)
        teacher_data = teacher_metrics.get(dataset, {})
        
        if student_metrics and teacher_data:
            # Calculate retention percentages
            acc_retention = (student_metrics['accuracy'] / teacher_data['accuracy'] * 100) if teacher_data['accuracy'] > 0 else 0
            f1_retention = (student_metrics['f1_score'] / teacher_data['f1_score'] * 100) if teacher_data['f1_score'] > 0 else 0
            
            comparison_data.append({
                'Dataset': dataset.upper(),
                'Teacher Accuracy (%)': f"{teacher_data['accuracy']:.2f}",
                'Student Accuracy (%)': f"{student_metrics['accuracy']:.2f}",
                'Accuracy Retention (%)': f"{acc_retention:.1f}",
                'Teacher F1 (%)': f"{teacher_data['f1_score']:.2f}",
                'Student F1 (%)': f"{student_metrics['f1_score']:.2f}",
                'F1 Retention (%)': f"{f1_retention:.1f}",
                'Student Valid/Total': f"{student_metrics['total_valid']}/{student_metrics['total_cases']}"
            })
        elif student_metrics:
            comparison_data.append({
                'Dataset': dataset.upper(),
                'Teacher Accuracy (%)': "N/A",
                'Student Accuracy (%)': f"{student_metrics['accuracy']:.2f}",
                'Accuracy Retention (%)': "N/A",
                'Teacher F1 (%)': "N/A",
                'Student F1 (%)': f"{student_metrics['f1_score']:.2f}",
                'F1 Retention (%)': "N/A",
                'Student Valid/Total': f"{student_metrics['total_valid']}/{student_metrics['total_cases']}"
            })
        else:
            print(f"⚠️  No student metrics found for {dataset}")
    
    if comparison_data:
        df = pd.DataFrame(comparison_data)
        print(f"\n{df.to_string(index=False)}")
        
        # Calculate averages
        print(f"\n📈 SUMMARY")
        print("-" * 30)
        
        total_student_acc = 0
        total_teacher_acc = 0
        total_student_f1 = 0
        total_teacher_f1 = 0
        valid_comparisons = 0
        
        for dataset in datasets:
            student_metrics = metrics_manager.load_metrics(student_model_name, dataset)
            teacher_data = teacher_metrics.get(dataset, {})
            
            if student_metrics and teacher_data:
                total_student_acc += student_metrics['accuracy']
                total_teacher_acc += teacher_data['accuracy']
                total_student_f1 += student_metrics['f1_score']
                total_teacher_f1 += teacher_data['f1_score']
                valid_comparisons += 1
        
        if valid_comparisons > 0:
            avg_student_acc = total_student_acc / valid_comparisons
            avg_teacher_acc = total_teacher_acc / valid_comparisons
            avg_student_f1 = total_student_f1 / valid_comparisons
            avg_teacher_f1 = total_teacher_f1 / valid_comparisons
            
            acc_retention = (avg_student_acc / avg_teacher_acc * 100) if avg_teacher_acc > 0 else 0
            f1_retention = (avg_student_f1 / avg_teacher_f1 * 100) if avg_teacher_f1 > 0 else 0
            
            print(f"Average Teacher Accuracy: {avg_teacher_acc:.2f}%")
            print(f"Average Student Accuracy: {avg_student_acc:.2f}%")
            print(f"Average Accuracy Retention: {acc_retention:.1f}%")
            print(f"Average Teacher F1: {avg_teacher_f1:.2f}%")
            print(f"Average Student F1: {avg_student_f1:.2f}%")
            print(f"Average F1 Retention: {f1_retention:.1f}%")
            
            # Performance assessment
            print(f"\n🎯 PERFORMANCE ASSESSMENT")
            print("-" * 30)
            if acc_retention >= 90:
                print("🟢 Excellent retention (≥90%)")
            elif acc_retention >= 80:
                print("🟡 Good retention (80-90%)")
            elif acc_retention >= 70:
                print("🟠 Fair retention (70-80%)")
            else:
                print("🔴 Poor retention (<70%)")
        
        # Save comparison
        output_path = f"./metrics/teacher_vs_{student_model_name}_comparison.csv"
        df.to_csv(output_path, index=False)
        print(f"\n💾 Comparison saved to: {output_path}")
        
    else:
        print("❌ No data available for comparison")

def compare_multiple_students(student_models: List[str]) -> None:
    """
    Compare multiple student models
    
    Args:
        student_models: List of student model names
    """
    print(f"\n📊 MULTI-MODEL COMPARISON")
    print(f"Models: {', '.join(student_models)}")
    print("="*60)
    
    metrics_manager = MetricsManager()
    
    # Generate comparison
    df = metrics_manager.compare_models(student_models)
    if not df.empty:
        print(f"\n{df.to_string(index=False)}")
        
        # Create visualizations
        print(f"\n📈 Creating comparison charts...")
        metrics_manager.create_comparison_chart(student_models, metric='accuracy')
        metrics_manager.create_comparison_chart(student_models, metric='f1_score')
        
        # Generate report
        metrics_manager.generate_summary_report(student_models)
        
        print(f"✅ Multi-model comparison completed! Check ./metrics/ for detailed results.")
    else:
        print("❌ No metrics found for the specified models")

def show_available_results() -> None:
    """Show available evaluation results"""
    print(f"\n📋 AVAILABLE EVALUATION RESULTS")
    print("="*40)
    
    metrics_manager = MetricsManager()
    available = metrics_manager.list_available_metrics()
    
    if available:
        # Group by model
        models = {}
        for item in available:
            model = item['model_name']
            if model not in models:
                models[model] = []
            models[model].append(item['dataset'])
        
        for model, datasets in models.items():
            print(f"\n🤖 {model}:")
            for dataset in sorted(datasets):
                print(f"   ✅ {dataset}")
    else:
        print("No evaluation results found.")
        print("Run evaluations first with: python run_evaluation.py")

def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(
        description="Compare model performance on benchmark datasets",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Compare teacher vs student
  python compare_performance.py --teacher-vs-student my_student_model
  
  # Compare multiple student models
  python compare_performance.py --compare-students model1 model2 model3
  
  # Show available results
  python compare_performance.py --show-results
        """
    )
    
    parser.add_argument('--teacher-vs-student', type=str, metavar='STUDENT_MODEL',
                       help='Compare teacher vs student model')
    parser.add_argument('--compare-students', nargs='+', metavar='MODEL',
                       help='Compare multiple student models')
    parser.add_argument('--benchmark-dir', type=str, default='./benchmark',
                       help='Directory containing teacher benchmark results')
    parser.add_argument('--show-results', action='store_true',
                       help='Show available evaluation results')
    
    args = parser.parse_args()
    
    if args.show_results:
        show_available_results()
    elif args.teacher_vs_student:
        compare_teacher_student(args.teacher_vs_student, args.benchmark_dir)
    elif args.compare_students:
        compare_multiple_students(args.compare_students)
    else:
        print("❌ Please specify an action")
        parser.print_help()

if __name__ == "__main__":
    main()
