#!/usr/bin/env python3
"""
Student Model Inference Script
Loads the distilled student model and runs inference on benchmark datasets.
"""

import os
import json
import torch
import pandas as pd
import numpy as np
from transformers import AutoModelForCausalLM, AutoTokenizer
from datasets import load_dataset
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import warnings
from typing import Dict, List, Any, Optional
import argparse
from datetime import datetime

warnings.filterwarnings("ignore")

class StudentModelInference:
    def __init__(self, model_path: str, device: str = "auto"):
        """Initialize student model for inference"""
        self.device = device if device != "auto" else ("cuda" if torch.cuda.is_available() else "cpu")
        
        print(f"Loading student model from: {model_path}")
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            device_map="auto" if torch.cuda.device_count() > 1 else self.device
        )
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        if not self.tokenizer.pad_token:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
        self.model.eval()
        print(f"Student model loaded on {self.device}")
    
    def generate_response(self, prompt: str, max_length: int = 512, temperature: float = 0.7) -> str:
        """Generate response from student model"""
        inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # Decode only the generated part
        generated_tokens = outputs[0][inputs['input_ids'].shape[1]:]
        response = self.tokenizer.decode(generated_tokens, skip_special_tokens=True)
        return response.strip()

class CaseholdEvaluator:
    def __init__(self, student_model: StudentModelInference):
        self.student_model = student_model
        self.dataset_context = """
        CaseHOLD is a multiple choice question answering task derived
        from legal citations in judicial rulings. The citing context from the
        judicial decision serves as the prompt for the question. The answer
        choices are holding statements derived from citations following
        text in a legal decision. The correct answer is the holding statement that corresponds
        to the citing text. The four incorrect answers are other holding
        statements.
        """
    
    def format_prompt(self, case_data: Dict) -> str:
        """Format case data into prompt for student model"""
        prompt = f"""
        {self.dataset_context}
        Given the following data from the casehold dataset: 
        {case_data['citing_prompt']} 
        with holdings:
        0: {case_data['holding_0']}
        1: {case_data['holding_1']}
        2: {case_data['holding_2']}
        3: {case_data['holding_3']}
        4: {case_data['holding_4']}

        Please provide the index of the correct holding statement, which must be a number between 0 and 4, inclusive.
        Only output the number (0, 1, 2, 3, or 4).
        Answer:"""
        return prompt
    
    def extract_answer(self, response: str) -> Optional[int]:
        """Extract answer from model response"""
        response = response.strip().lower()
        
        # Try to find a number in the response
        for char in response:
            if char.isdigit():
                num = int(char)
                if 0 <= num <= 4:
                    return num
        
        # Try to find written numbers
        number_words = {'zero': 0, 'one': 1, 'two': 2, 'three': 3, 'four': 4}
        for word, num in number_words.items():
            if word in response:
                return num
        
        return None
    
    def evaluate(self, dataset_size: int = 1000) -> Dict[str, float]:
        """Evaluate student model on CaseHOLD dataset"""
        print(f"Evaluating CaseHOLD dataset (size: {dataset_size})")
        
        # Load dataset
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "casehold")
        data = ds['train'].select(range(min(dataset_size, len(ds['train']))))
        
        y_true = []
        y_pred = []
        invalid_cases = 0
        
        for i, case_data in enumerate(data):
            if i % 100 == 0:
                print(f"Processing case {i+1}/{len(data)}")
            
            prompt = self.format_prompt(case_data)
            response = self.student_model.generate_response(prompt, max_length=50, temperature=0.1)
            predicted_answer = self.extract_answer(response)
            
            if predicted_answer is not None:
                y_true.append(case_data['label'])
                y_pred.append(predicted_answer)
            else:
                invalid_cases += 1
                print(f"Invalid response for case {i}: {response}")
        
        # Calculate metrics
        if len(y_true) > 0:
            accuracy = accuracy_score(y_true, y_pred)
            precision = precision_score(y_true, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_true, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0)
            f1_macro = f1_score(y_true, y_pred, average='macro', zero_division=0)
            
            metrics = {
                "accuracy": accuracy * 100,
                "precision": precision * 100,
                "recall": recall * 100,
                "f1_score": f1 * 100,
                "f1_macro": f1_macro * 100,
                "invalid_cases": invalid_cases,
                "total_cases": len(data),
                "valid_cases": len(y_true)
            }
        else:
            metrics = {
                "accuracy": 0.0,
                "precision": 0.0,
                "recall": 0.0,
                "f1_score": 0.0,
                "f1_macro": 0.0,
                "invalid_cases": invalid_cases,
                "total_cases": len(data),
                "valid_cases": 0
            }
        
        return metrics

class PubmedEvaluator:
    def __init__(self, student_model: StudentModelInference):
        self.student_model = student_model
        self.dataset_context = """
        PubMedQA is a biomedical question answering dataset. Given a question
        and relevant context from PubMed abstracts, the task is to answer
        whether the statement is true (yes), false (no), or uncertain (maybe).
        """
    
    def format_prompt(self, case_data: Dict) -> str:
        """Format case data into prompt for student model"""
        prompt = f"""
        {self.dataset_context}
        Given the following question from the PubMedQA dataset: 
        Question: {case_data['question']}
        Context: {case_data['context']}
        Long Answer: {case_data['long_answer']}
        
        Please provide the final decision based on these data. It must be either "yes", "no", or "maybe".
        Answer:"""
        return prompt
    
    def extract_answer(self, response: str) -> Optional[str]:
        """Extract answer from model response"""
        response = response.strip().lower()
        
        # Look for exact matches first
        if 'yes' in response and 'no' not in response:
            return 'yes'
        elif 'no' in response and 'yes' not in response:
            return 'no'
        elif 'maybe' in response:
            return 'maybe'
        
        return None
    
    def evaluate(self, dataset_size: int = 1000) -> Dict[str, float]:
        """Evaluate student model on PubMedQA dataset"""
        print(f"Evaluating PubMedQA dataset (size: {dataset_size})")
        
        # Load dataset
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "pubmedqa")
        data = ds['train'].select(range(min(dataset_size, len(ds['train']))))
        
        y_true = []
        y_pred = []
        invalid_cases = 0
        
        for i, case_data in enumerate(data):
            if i % 100 == 0:
                print(f"Processing case {i+1}/{len(data)}")
            
            prompt = self.format_prompt(case_data)
            response = self.student_model.generate_response(prompt, max_length=50, temperature=0.1)
            predicted_answer = self.extract_answer(response)
            
            if predicted_answer is not None:
                y_true.append(case_data['final_decision'])
                y_pred.append(predicted_answer)
            else:
                invalid_cases += 1
                print(f"Invalid response for case {i}: {response}")
        
        # Calculate metrics
        if len(y_true) > 0:
            accuracy = accuracy_score(y_true, y_pred)
            precision = precision_score(y_true, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_true, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0)
            f1_macro = f1_score(y_true, y_pred, average='macro', zero_division=0)
            
            metrics = {
                "accuracy": accuracy * 100,
                "precision": precision * 100,
                "recall": recall * 100,
                "f1_score": f1 * 100,
                "f1_macro": f1_macro * 100,
                "invalid_cases": invalid_cases,
                "total_cases": len(data),
                "valid_cases": len(y_true)
            }
        else:
            metrics = {
                "accuracy": 0.0,
                "precision": 0.0,
                "recall": 0.0,
                "f1_score": 0.0,
                "f1_macro": 0.0,
                "invalid_cases": invalid_cases,
                "total_cases": len(data),
                "valid_cases": 0
            }
        
        return metrics

class FinqaEvaluator:
    def __init__(self, student_model: StudentModelInference):
        self.student_model = student_model
        self.dataset_context = """
        FinQA is a financial question answering dataset. Given a question
        about financial data and relevant context, the task is to provide
        a numerical answer or calculation result.
        """

    def format_prompt(self, case_data: Dict) -> str:
        """Format case data into prompt for student model"""
        prompt = f"""
        {self.dataset_context}
        Given the following question from the FinQA dataset:
        Question: {case_data['question']}
        Context: {case_data.get('context', case_data.get('pre_text', ''))}
        Table: {case_data.get('table', '')}

        Please provide a numerical answer to this question.
        Answer:"""
        return prompt

    def extract_answer(self, response: str) -> Optional[float]:
        """Extract numerical answer from model response"""
        import re

        # Look for numbers in the response
        numbers = re.findall(r'-?\d+\.?\d*', response)
        if numbers:
            try:
                return float(numbers[0])
            except ValueError:
                pass

        return None

    def evaluate(self, dataset_size: int = 1000) -> Dict[str, float]:
        """Evaluate student model on FinQA dataset"""
        print(f"Evaluating FinQA dataset (size: {dataset_size})")

        # Load dataset
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "finqa")
        data = ds['train'].select(range(min(dataset_size, len(ds['train']))))

        correct_answers = 0
        total_valid = 0
        invalid_cases = 0

        for i, case_data in enumerate(data):
            if i % 100 == 0:
                print(f"Processing case {i+1}/{len(data)}")

            prompt = self.format_prompt(case_data)
            response = self.student_model.generate_response(prompt, max_length=100, temperature=0.1)
            predicted_answer = self.extract_answer(response)

            if predicted_answer is not None:
                # For FinQA, we'll use a simple accuracy check
                true_answer = float(case_data.get('answer', case_data.get('exe_ans', 0)))
                if abs(predicted_answer - true_answer) < 0.01:  # Allow small floating point differences
                    correct_answers += 1
                total_valid += 1
            else:
                invalid_cases += 1
                print(f"Invalid response for case {i}: {response}")

        # Calculate metrics
        accuracy = (correct_answers / total_valid * 100) if total_valid > 0 else 0.0

        metrics = {
            "accuracy": accuracy,
            "precision": accuracy,  # For numerical tasks, precision = accuracy
            "recall": accuracy,     # For numerical tasks, recall = accuracy
            "f1_score": accuracy,   # For numerical tasks, f1 = accuracy
            "f1_macro": accuracy,
            "invalid_cases": invalid_cases,
            "total_cases": len(data),
            "valid_cases": total_valid,
            "correct_answers": correct_answers
        }

        return metrics

def save_metrics(metrics: Dict[str, Any], dataset_name: str, model_path: str, output_dir: str = "./student_metrics"):
    """Save metrics to CSV and JSON files"""
    os.makedirs(output_dir, exist_ok=True)

    # Add metadata
    metrics_with_meta = {
        "dataset": dataset_name,
        "model_path": model_path,
        "timestamp": datetime.now().isoformat(),
        **metrics
    }

    # Save to JSON
    json_path = os.path.join(output_dir, f"{dataset_name}_student_metrics.json")
    with open(json_path, 'w') as f:
        json.dump(metrics_with_meta, f, indent=2)

    # Save to CSV
    csv_path = os.path.join(output_dir, f"{dataset_name}_student_metrics.csv")
    df = pd.DataFrame([metrics_with_meta])
    df.to_csv(csv_path, index=False)

    print(f"Metrics saved to {json_path} and {csv_path}")

def main():
    parser = argparse.ArgumentParser(description='Run student model inference on benchmark datasets')
    parser.add_argument('--model-path', required=True, help='Path to the distilled student model')
    parser.add_argument('--datasets', nargs='+', default=['casehold', 'pubmed', 'finqa'],
                       choices=['casehold', 'pubmed', 'finqa'], help='Datasets to evaluate')
    parser.add_argument('--dataset-size', type=int, default=1000, help='Number of samples to evaluate per dataset')
    parser.add_argument('--output-dir', default='./student_metrics', help='Output directory for metrics')
    parser.add_argument('--device', default='auto', help='Device to run inference on')

    args = parser.parse_args()

    # Initialize student model
    student_model = StudentModelInference(args.model_path, args.device)

    # Run evaluations
    all_metrics = {}

    for dataset_name in args.datasets:
        print(f"\n{'='*50}")
        print(f"Evaluating {dataset_name.upper()} dataset")
        print(f"{'='*50}")

        if dataset_name == 'casehold':
            evaluator = CaseholdEvaluator(student_model)
        elif dataset_name == 'pubmed':
            evaluator = PubmedEvaluator(student_model)
        elif dataset_name == 'finqa':
            evaluator = FinqaEvaluator(student_model)
        else:
            print(f"Unknown dataset: {dataset_name}")
            continue

        metrics = evaluator.evaluate(args.dataset_size)
        all_metrics[dataset_name] = metrics

        # Print results
        print(f"\nResults for {dataset_name}:")
        print(f"Accuracy: {metrics['accuracy']:.2f}%")
        print(f"Precision: {metrics['precision']:.2f}%")
        print(f"Recall: {metrics['recall']:.2f}%")
        print(f"F1 Score: {metrics['f1_score']:.2f}%")
        print(f"Macro F1: {metrics['f1_macro']:.2f}%")
        print(f"Valid Cases: {metrics['valid_cases']}/{metrics['total_cases']}")
        print(f"Invalid Cases: {metrics['invalid_cases']}")

        # Save metrics
        save_metrics(metrics, dataset_name, args.model_path, args.output_dir)

    # Save combined metrics
    combined_metrics = {
        "model_path": args.model_path,
        "timestamp": datetime.now().isoformat(),
        "datasets": all_metrics
    }

    combined_path = os.path.join(args.output_dir, "combined_student_metrics.json")
    with open(combined_path, 'w') as f:
        json.dump(combined_metrics, f, indent=2)

    print(f"\nCombined metrics saved to {combined_path}")
    print("\nStudent model evaluation completed!")

if __name__ == "__main__":
    main()
