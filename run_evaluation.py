#!/usr/bin/env python3
"""
Clean Evaluation Runner
Simple interface to run student model evaluation on benchmark datasets.
"""

import argparse
import sys
from typing import List, Optional
from model_manager import StudentModelManager, list_available_models, get_model_path
from evaluators import get_evaluator
from metrics_manager import MetricsManager

def run_single_evaluation(model_path: str, dataset_name: str, dataset_size: int = 1000, 
                         device: str = "auto") -> bool:
    """
    Run evaluation on a single dataset
    
    Args:
        model_path: Path to the student model
        dataset_name: Name of the dataset ('casehold', 'pubmed', 'finqa')
        dataset_size: Number of examples to evaluate
        device: Device to run on
        
    Returns:
        True if successful, False otherwise
    """
    print(f"\n{'='*60}")
    print(f"EVALUATING {dataset_name.upper()} DATASET")
    print(f"{'='*60}")
    
    # Load model
    print("Loading model...")
    manager = StudentModelManager(model_path, device)
    if not manager.load_model():
        print(f"❌ Failed to load model from {model_path}")
        return False
    
    # Get evaluator
    try:
        evaluator = get_evaluator(dataset_name, manager.model, manager.tokenizer)
    except ValueError as e:
        print(f"❌ {e}")
        return False
    
    # Run evaluation
    try:
        metrics = evaluator.evaluate(dataset_size)
        
        # Print results
        print(f"\n📊 RESULTS FOR {dataset_name.upper()}:")
        print(f"   Accuracy:     {metrics['accuracy']:.2f}%")
        print(f"   Precision:    {metrics['precision']:.2f}%")
        print(f"   Recall:       {metrics['recall']:.2f}%")
        print(f"   F1 Score:     {metrics['f1_score']:.2f}%")
        print(f"   Macro F1:     {metrics['f1_macro']:.2f}%")
        print(f"   Valid Cases:  {metrics['total_valid']}/{metrics['total_cases']}")
        print(f"   Invalid Cases: {metrics['invalid_cases']}")
        
        # Save metrics
        metrics_manager = MetricsManager()
        model_name = model_path.split('/')[-1] if '/' in model_path else model_path
        metrics_manager.save_metrics(metrics, model_name, dataset_name)
        
        print(f"✅ Evaluation completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        return False

def run_all_evaluations(model_path: str, dataset_size: int = 1000, 
                       device: str = "auto") -> bool:
    """
    Run evaluation on all benchmark datasets
    
    Args:
        model_path: Path to the student model
        dataset_size: Number of examples to evaluate per dataset
        device: Device to run on
        
    Returns:
        True if all evaluations successful, False otherwise
    """
    datasets = ['casehold', 'pubmed', 'finqa']
    results = []
    
    print(f"\n🚀 RUNNING EVALUATION ON ALL DATASETS")
    print(f"Model: {model_path}")
    print(f"Dataset size: {dataset_size} examples per dataset")
    print(f"Device: {device}")
    
    for dataset in datasets:
        success = run_single_evaluation(model_path, dataset, dataset_size, device)
        results.append(success)
    
    # Summary
    successful = sum(results)
    total = len(results)
    
    print(f"\n{'='*60}")
    print(f"EVALUATION SUMMARY")
    print(f"{'='*60}")
    print(f"Successful evaluations: {successful}/{total}")
    
    if successful == total:
        print("🎉 All evaluations completed successfully!")
        return True
    else:
        print("⚠️  Some evaluations failed.")
        return False

def compare_models(model_names: List[str]) -> None:
    """
    Compare performance across multiple models
    
    Args:
        model_names: List of model names to compare
    """
    print(f"\n📊 COMPARING MODELS")
    print(f"Models: {', '.join(model_names)}")
    
    metrics_manager = MetricsManager()
    
    # Generate comparison table
    df = metrics_manager.compare_models(model_names)
    if not df.empty:
        print(f"\n{df.to_string(index=False)}")
        
        # Create charts
        metrics_manager.create_comparison_chart(model_names, metric='accuracy')
        metrics_manager.create_comparison_chart(model_names, metric='f1_score')
        
        # Generate summary report
        metrics_manager.generate_summary_report(model_names)
        metrics_manager.export_to_csv(model_names)
        
        print(f"\n✅ Comparison completed! Check ./metrics/ for detailed results.")
    else:
        print("❌ No metrics found for comparison.")

def list_models() -> None:
    """List available models"""
    print("\n📋 AVAILABLE MODELS")
    print("-" * 40)
    
    # List local models
    local_models = list_available_models()
    if local_models:
        print("Local models:")
        for i, model_path in enumerate(local_models, 1):
            print(f"  {i}. {model_path}")
    else:
        print("No local models found in ./distilled_student/")
    
    # List common models
    print("\nCommon model names:")
    common_models = {
        "llama2-1b": "meta-llama/Llama-2-1b-hf",
        "llama2-7b": "meta-llama/Llama-2-7b-hf", 
        "llama2-13b": "meta-llama/Llama-2-13b-hf",
        "llama3.2-1b": "meta-llama/Llama-3.2-1B",
        "llama3.2-3b": "meta-llama/Llama-3.2-3B"
    }
    
    for name, path in common_models.items():
        print(f"  {name} -> {path}")

def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(
        description="Run student model evaluation on benchmark datasets",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Evaluate single dataset
  python run_evaluation.py --model ./distilled_student/final_student_model --dataset casehold
  
  # Evaluate all datasets
  python run_evaluation.py --model llama2-1b --all-datasets
  
  # Compare multiple models
  python run_evaluation.py --compare student_model teacher_model
  
  # List available models
  python run_evaluation.py --list-models
        """
    )
    
    parser.add_argument('--model', type=str, help='Path to student model or model name')
    parser.add_argument('--dataset', type=str, choices=['casehold', 'pubmed', 'finqa'],
                       help='Dataset to evaluate on')
    parser.add_argument('--all-datasets', action='store_true',
                       help='Evaluate on all datasets')
    parser.add_argument('--dataset-size', type=int, default=1000,
                       help='Number of examples to evaluate (default: 1000)')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to run on (auto, cuda, cpu)')
    parser.add_argument('--compare', nargs='+', metavar='MODEL',
                       help='Compare multiple models')
    parser.add_argument('--list-models', action='store_true',
                       help='List available models')
    
    args = parser.parse_args()
    
    # Handle different modes
    if args.list_models:
        list_models()
        return
    
    if args.compare:
        compare_models(args.compare)
        return
    
    if not args.model:
        print("❌ Please specify a model with --model")
        parser.print_help()
        sys.exit(1)
    
    # Get model path
    model_path = get_model_path(args.model)
    
    # Run evaluation
    if args.all_datasets:
        success = run_all_evaluations(model_path, args.dataset_size, args.device)
    elif args.dataset:
        success = run_single_evaluation(model_path, args.dataset, args.dataset_size, args.device)
    else:
        print("❌ Please specify --dataset or --all-datasets")
        parser.print_help()
        sys.exit(1)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
