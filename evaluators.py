#!/usr/bin/env python3
"""
Benchmark Dataset Evaluators
Clean, modular evaluators for each benchmark dataset.
"""

import torch
import pandas as pd
import numpy as np
from datasets import load_dataset
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from typing import Dict, List, Any, Optional
import re
from abc import ABC, abstractmethod

class BaseEvaluator(ABC):
    """Base class for dataset evaluators"""
    
    def __init__(self, model, tokenizer, dataset_name: str):
        self.model = model
        self.tokenizer = tokenizer
        self.dataset_name = dataset_name
        self.device = next(model.parameters()).device
    
    @abstractmethod
    def load_dataset(self, size: int = 1000):
        """Load the dataset"""
        pass
    
    @abstractmethod
    def format_prompt(self, example: Dict) -> str:
        """Format example into prompt"""
        pass
    
    @abstractmethod
    def extract_answer(self, response: str) -> Optional[Any]:
        """Extract answer from model response"""
        pass
    
    @abstractmethod
    def is_correct(self, predicted: Any, actual: Any) -> bool:
        """Check if prediction is correct"""
        pass
    
    def generate_response(self, prompt: str, max_length: int = 100, temperature: float = 0.1) -> str:
        """Generate response from model"""
        inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=temperature,
                do_sample=True if temperature > 0 else False,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # Decode only the generated part
        generated_tokens = outputs[0][inputs['input_ids'].shape[1]:]
        response = self.tokenizer.decode(generated_tokens, skip_special_tokens=True)
        return response.strip()
    
    def evaluate(self, dataset_size: int = 1000) -> Dict[str, Any]:
        """Evaluate model on dataset"""
        print(f"Evaluating {self.dataset_name} dataset (size: {dataset_size})")
        
        # Load dataset
        dataset = self.load_dataset(dataset_size)
        
        correct_predictions = 0
        total_valid = 0
        invalid_cases = 0
        predictions = []
        actuals = []
        
        for i, example in enumerate(dataset):
            if i % 100 == 0:
                print(f"Processing example {i+1}/{len(dataset)}")
            
            # Generate prediction
            prompt = self.format_prompt(example)
            response = self.generate_response(prompt)
            predicted = self.extract_answer(response)
            
            if predicted is not None:
                actual = self.get_ground_truth(example)
                actuals.append(actual)
                predictions.append(predicted)
                
                if self.is_correct(predicted, actual):
                    correct_predictions += 1
                total_valid += 1
            else:
                invalid_cases += 1
                print(f"Invalid response for example {i}: {response[:100]}...")
        
        # Calculate metrics
        accuracy = (correct_predictions / total_valid * 100) if total_valid > 0 else 0.0
        
        # For classification tasks, calculate additional metrics
        if len(set(actuals)) > 1 and len(set(predictions)) > 1:
            try:
                precision = precision_score(actuals, predictions, average='weighted', zero_division=0) * 100
                recall = recall_score(actuals, predictions, average='weighted', zero_division=0) * 100
                f1 = f1_score(actuals, predictions, average='weighted', zero_division=0) * 100
                f1_macro = f1_score(actuals, predictions, average='macro', zero_division=0) * 100
            except:
                precision = recall = f1 = f1_macro = accuracy
        else:
            precision = recall = f1 = f1_macro = accuracy
        
        metrics = {
            "dataset": self.dataset_name,
            "accuracy": accuracy,
            "precision": precision,
            "recall": recall,
            "f1_score": f1,
            "f1_macro": f1_macro,
            "correct_predictions": correct_predictions,
            "total_valid": total_valid,
            "invalid_cases": invalid_cases,
            "total_cases": len(dataset)
        }
        
        return metrics
    
    @abstractmethod
    def get_ground_truth(self, example: Dict) -> Any:
        """Get ground truth from example"""
        pass

class CaseholdEvaluator(BaseEvaluator):
    """Evaluator for CaseHOLD legal reasoning dataset"""
    
    def __init__(self, model, tokenizer):
        super().__init__(model, tokenizer, "casehold")
    
    def load_dataset(self, size: int = 1000):
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "casehold")
        return ds['train'].select(range(min(size, len(ds['train']))))
    
    def format_prompt(self, example: Dict) -> str:
        return f"""Given the following legal case context and holding statements, select the correct holding.

Context: {example['citing_prompt']}

Holdings:
0: {example['holding_0']}
1: {example['holding_1']}
2: {example['holding_2']}
3: {example['holding_3']}
4: {example['holding_4']}

Please provide the index of the correct holding statement (0, 1, 2, 3, or 4).
Answer:"""
    
    def extract_answer(self, response: str) -> Optional[int]:
        response = response.strip().lower()
        
        # Look for digit
        for char in response:
            if char.isdigit():
                num = int(char)
                if 0 <= num <= 4:
                    return num
        
        # Look for written numbers
        number_words = {'zero': 0, 'one': 1, 'two': 2, 'three': 3, 'four': 4}
        for word, num in number_words.items():
            if word in response:
                return num
        
        return None
    
    def is_correct(self, predicted: int, actual: int) -> bool:
        return predicted == actual
    
    def get_ground_truth(self, example: Dict) -> int:
        return example['label']

class PubmedEvaluator(BaseEvaluator):
    """Evaluator for PubMedQA biomedical dataset"""
    
    def __init__(self, model, tokenizer):
        super().__init__(model, tokenizer, "pubmed")
    
    def load_dataset(self, size: int = 1000):
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "pubmedqa")
        return ds['train'].select(range(min(size, len(ds['train']))))
    
    def format_prompt(self, example: Dict) -> str:
        return f"""Answer the following biomedical question based on the provided context.

Question: {example['question']}

Context: {example['context']}

Long Answer: {example['long_answer']}

Please provide the final decision. It must be either "yes", "no", or "maybe".
Answer:"""
    
    def extract_answer(self, response: str) -> Optional[str]:
        response = response.strip().lower()
        
        if 'yes' in response and 'no' not in response:
            return 'yes'
        elif 'no' in response and 'yes' not in response:
            return 'no'
        elif 'maybe' in response:
            return 'maybe'
        
        return None
    
    def is_correct(self, predicted: str, actual: str) -> bool:
        return predicted == actual
    
    def get_ground_truth(self, example: Dict) -> str:
        return example['final_decision']

class FinqaEvaluator(BaseEvaluator):
    """Evaluator for FinQA financial reasoning dataset"""
    
    def __init__(self, model, tokenizer):
        super().__init__(model, tokenizer, "finqa")
    
    def load_dataset(self, size: int = 1000):
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "finqa")
        return ds['train'].select(range(min(size, len(ds['train']))))
    
    def format_prompt(self, example: Dict) -> str:
        context = example.get('context', example.get('pre_text', ''))
        table = example.get('table', '')
        
        return f"""Solve the following financial question using the provided context and table.

Question: {example['question']}

Context: {context}

Table: {table}

Please provide a numerical answer.
Answer:"""
    
    def extract_answer(self, response: str) -> Optional[float]:
        # Look for numbers in the response
        numbers = re.findall(r'-?\d+\.?\d*', response)
        if numbers:
            try:
                return float(numbers[0])
            except ValueError:
                pass
        return None
    
    def is_correct(self, predicted: float, actual: float) -> bool:
        return abs(predicted - actual) < 0.01  # Allow small floating point differences
    
    def get_ground_truth(self, example: Dict) -> float:
        answer = example.get('answer', example.get('exe_ans', 0))
        return float(answer)

def get_evaluator(dataset_name: str, model, tokenizer):
    """Factory function to get the appropriate evaluator"""
    evaluators = {
        'casehold': CaseholdEvaluator,
        'pubmed': PubmedEvaluator,
        'finqa': FinqaEvaluator
    }
    
    if dataset_name not in evaluators:
        raise ValueError(f"Unknown dataset: {dataset_name}. Available: {list(evaluators.keys())}")
    
    return evaluators[dataset_name](model, tokenizer)
