import random
from typing import List, Optional
from student_trainer import RemoteTeacherClient
import torch

class DistributedTeacherClient:
    """
    Distributes requests across multiple teacher servers for logits distillation.
    """
    def __init__(self, server_addresses: List[str]):
        self.clients = [RemoteTeacherClient(addr) for addr in server_addresses]
        self.num_clients = len(self.clients)
        self.counter = 0

    def get_teacher_logits(self, input_ids: torch.Tensor, attention_mask: torch.Tensor) -> Optional[torch.Tensor]:
        # Round-robin distribution
        client = self.clients[self.counter % self.num_clients]
        self.counter += 1
        return client.get_teacher_logits(input_ids, attention_mask)

    def close(self):
        for client in self.clients:
            client.close()
