#!/bin/bash
"""
FinQA Evaluation Runner
Runs student model evaluation on FinQA dataset in tmux session.
"""

# Configuration
MODEL_PATH="${1:-./distilled_student/final_student_model}"
DATASET_SIZE="${2:-1000}"
LOG_INTERVAL="${3:-10}"
SESSION_NAME="finqa_eval"

# Check if model path is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <model_path> [dataset_size] [log_interval]"
    echo "Example: $0 ./distilled_student/final_student_model 1000 10"
    echo ""
    echo "Using default model path: $MODEL_PATH"
fi

echo "=============================================="
echo "FINQA STUDENT MODEL EVALUATION"
echo "=============================================="
echo "Model Path: $MODEL_PATH"
echo "Dataset Size: $DATASET_SIZE"
echo "Log Interval: $LOG_INTERVAL"
echo "Session Name: $SESSION_NAME"
echo "Output Directory: benchmark/finqa/"
echo "=============================================="

# Check if tmux session already exists
if tmux has-session -t $SESSION_NAME 2>/dev/null; then
    echo "⚠️  Tmux session '$SESSION_NAME' already exists!"
    echo "Options:"
    echo "1. Kill existing session and start new one (y/n)?"
    echo "2. Attach to existing session (a)?"
    echo "3. Cancel (c)?"
    read -p "Choose option: " choice
    
    case $choice in
        [Yy]* )
            echo "Killing existing session..."
            tmux kill-session -t $SESSION_NAME
            ;;
        [Aa]* )
            echo "Attaching to existing session..."
            tmux attach-session -t $SESSION_NAME
            exit 0
            ;;
        * )
            echo "Cancelled."
            exit 1
            ;;
    esac
fi

# Create output directory if it doesn't exist
mkdir -p benchmark/finqa

# Create tmux session and run evaluation
echo "🚀 Starting FinQA evaluation in tmux session '$SESSION_NAME'..."

tmux new-session -d -s $SESSION_NAME -c "$(pwd)/benchmark/finqa" \
    "python student.py \
    --model-path '$MODEL_PATH' \
    --dataset-size $DATASET_SIZE \
    --log-interval $LOG_INTERVAL \
    --output-file student_metrics.csv \
    --device auto; \
    echo ''; \
    echo '🎉 FinQA evaluation completed!'; \
    echo 'Results saved to benchmark/finqa/student_metrics.csv'; \
    echo 'Press any key to exit...'; \
    read"

echo "✅ Tmux session '$SESSION_NAME' created successfully!"
echo ""
echo "To monitor progress:"
echo "  tmux attach-session -t $SESSION_NAME"
echo ""
echo "To check if session is running:"
echo "  tmux list-sessions | grep $SESSION_NAME"
echo ""
echo "To kill the session:"
echo "  tmux kill-session -t $SESSION_NAME"
echo ""
echo "Output files will be saved to:"
echo "  benchmark/finqa/student_metrics.csv"
echo "  benchmark/finqa/student_metrics_summary.json"

# Optionally attach to the session immediately
read -p "Attach to session now? (y/n): " attach_now
if [[ $attach_now =~ ^[Yy]$ ]]; then
    tmux attach-session -t $SESSION_NAME
fi
