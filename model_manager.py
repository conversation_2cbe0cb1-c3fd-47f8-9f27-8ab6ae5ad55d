#!/usr/bin/env python3
"""
Model Manager
Clean interface for loading and managing student models.
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from typing import Optional
import os

class StudentModelManager:
    """Manages student model loading and inference"""
    
    def __init__(self, model_path: str, device: str = "auto"):
        """
        Initialize student model manager
        
        Args:
            model_path: Path to the student model (local path or HuggingFace model name)
            device: Device to load model on ("auto", "cuda", "cpu")
        """
        self.model_path = model_path
        self.device = self._get_device(device)
        self.model = None
        self.tokenizer = None
        
    def _get_device(self, device: str) -> str:
        """Determine the appropriate device"""
        if device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device
    
    def load_model(self):
        """Load the student model and tokenizer"""
        print(f"Loading student model from: {self.model_path}")
        print(f"Using device: {self.device}")
        
        try:
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                torch_dtype=torch.bfloat16,
                device_map="auto" if torch.cuda.device_count() > 1 else self.device,
                trust_remote_code=True
            )
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            # Set pad token if not present
            if not self.tokenizer.pad_token:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Set model to evaluation mode
            self.model.eval()
            
            print(f"✅ Model loaded successfully!")
            print(f"   Parameters: {self.get_parameter_count():,}")
            print(f"   Vocab size: {self.model.config.vocab_size:,}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            return False
    
    def get_parameter_count(self) -> int:
        """Get total number of parameters"""
        if self.model is None:
            return 0
        return sum(p.numel() for p in self.model.parameters())
    
    def is_loaded(self) -> bool:
        """Check if model is loaded"""
        return self.model is not None and self.tokenizer is not None
    
    def get_model_info(self) -> dict:
        """Get model information"""
        if not self.is_loaded():
            return {"status": "not_loaded"}
        
        return {
            "status": "loaded",
            "model_path": self.model_path,
            "device": self.device,
            "parameters": self.get_parameter_count(),
            "vocab_size": self.model.config.vocab_size,
            "model_type": self.model.config.model_type if hasattr(self.model.config, 'model_type') else "unknown"
        }

def load_student_model(model_path: str, device: str = "auto") -> Optional[StudentModelManager]:
    """
    Convenience function to load a student model
    
    Args:
        model_path: Path to the student model
        device: Device to load on
        
    Returns:
        StudentModelManager instance if successful, None otherwise
    """
    manager = StudentModelManager(model_path, device)
    
    if manager.load_model():
        return manager
    else:
        return None

def list_available_models(base_dir: str = "./distilled_student") -> list:
    """
    List available student models in the base directory
    
    Args:
        base_dir: Base directory to search for models
        
    Returns:
        List of available model paths
    """
    available_models = []
    
    if not os.path.exists(base_dir):
        return available_models
    
    for item in os.listdir(base_dir):
        model_path = os.path.join(base_dir, item)
        
        # Check if it's a directory with model files
        if os.path.isdir(model_path):
            # Look for common model files
            model_files = ['pytorch_model.bin', 'model.safetensors', 'config.json']
            if any(os.path.exists(os.path.join(model_path, f)) for f in model_files):
                available_models.append(model_path)
    
    return available_models

def get_model_size_info(model_path: str) -> dict:
    """
    Get model size information without loading the full model
    
    Args:
        model_path: Path to the model
        
    Returns:
        Dictionary with size information
    """
    try:
        from transformers import AutoConfig
        config = AutoConfig.from_pretrained(model_path)
        
        # Estimate parameters based on config
        vocab_size = getattr(config, 'vocab_size', 0)
        hidden_size = getattr(config, 'hidden_size', 0)
        num_layers = getattr(config, 'num_hidden_layers', 0)
        
        return {
            "vocab_size": vocab_size,
            "hidden_size": hidden_size,
            "num_layers": num_layers,
            "model_type": getattr(config, 'model_type', 'unknown')
        }
    except Exception as e:
        return {"error": str(e)}

# Predefined model configurations
COMMON_MODELS = {
    "llama2-1b": "meta-llama/Llama-2-1b-hf",
    "llama2-7b": "meta-llama/Llama-2-7b-hf", 
    "llama2-13b": "meta-llama/Llama-2-13b-hf",
    "llama3.2-1b": "meta-llama/Llama-3.2-1B",
    "llama3.2-3b": "meta-llama/Llama-3.2-3B"
}

def get_model_path(model_name: str) -> str:
    """
    Get model path from common model names or return as-is
    
    Args:
        model_name: Model name or path
        
    Returns:
        Model path
    """
    return COMMON_MODELS.get(model_name, model_name)
