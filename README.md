# Student Model Evaluation on Benchmark Datasets

Simple system to run student model inference on benchmark datasets (CaseHOLD, PubMedQA, FinQA) with incremental logging.

## 🎯 **Key Features**

- **Individual dataset evaluation**: Separate scripts for each benchmark dataset
- **Incremental logging**: Metrics logged every 10 samples to CSV files
- **Tmux integration**: Run evaluations in separate tmux sessions
- **Real-time monitoring**: Track progress across all evaluations

## 📁 **File Structure**

```
./
├── benchmark/
│   ├── casehold/
│   │   ├── student.py          # CaseHOLD evaluation script
│   │   └── teacher.py          # Teacher baseline (existing)
│   ├── pubmed/
│   │   ├── student.py          # PubMedQA evaluation script
│   │   └── teacher.py          # Teacher baseline (existing)
│   └── finqa/
│       ├── student.py          # FinQA evaluation script
│       └── teacher.py          # Teacher baseline (existing)
├── run_casehold.py             # CaseHOLD Python runner
├── run_pubmed.py               # PubMedQA Python runner
└── run_finqa.py                # FinQA Python runner
```

## 🚀 **Quick Start**

### 1. Run Single Dataset Evaluation

#### CaseHOLD (Legal reasoning):
```bash
python run_casehold.py ./path/to/student/model
python run_casehold.py ./path/to/student/model --dataset-size 1000 --log-interval 10
```

#### PubMedQA (Biomedical QA):
```bash
python run_pubmed.py ./path/to/student/model
python run_pubmed.py ./path/to/student/model --dataset-size 1000 --log-interval 10
```

#### FinQA (Financial reasoning):
```bash
python run_finqa.py ./path/to/student/model
python run_finqa.py ./path/to/student/model --dataset-size 1000 --log-interval 10
```

### 2. Monitor Progress
```bash
# Check metrics files in real-time
tail -f benchmark/casehold/student_metrics.csv
tail -f benchmark/pubmed/student_metrics.csv
tail -f benchmark/finqa/student_metrics.csv
```

## 📊 **Dataset Information**

### CaseHOLD (Legal Reasoning)
- **Task**: Multiple choice legal reasoning (5-way classification: 0-4)
- **Output**: `benchmark/casehold/student_metrics.csv`
- **Metrics**: Accuracy, Precision, Recall, F1-score

### PubMedQA (Biomedical QA)
- **Task**: Biomedical question answering (3-way: yes/no/maybe)
- **Output**: `benchmark/pubmed/student_metrics.csv`
- **Metrics**: Accuracy, Precision, Recall, F1-score

### FinQA (Financial Reasoning)
- **Task**: Financial numerical reasoning (numerical answers)
- **Output**: `benchmark/finqa/student_metrics.csv`
- **Metrics**: Accuracy, MAE, RMSE

## 📈 **Output Files**

Each evaluation generates:
- **`student_metrics.csv`**: Incremental metrics logged every 10 samples
- **`student_metrics_summary.json`**: Final summary with detailed results

### Sample CSV Output:
```csv
Sample,Accuracy,Precision,Recall,F1 Score,Macro F1 Score,Valid Cases,Invalid Cases,Total Cases,Timestamp,Model Path
10,75.000,75.000,75.000,75.000,75.000,8,2,10,2024-01-15T10:30:00,./model
20,78.500,78.200,78.500,78.350,78.100,17,3,20,2024-01-15T10:31:00,./model
```

## 🔧 **Script Parameters**

All Python scripts accept these parameters:
```bash
python run_[dataset].py <model_path> [options]
```

**Required:**
- **model_path**: Path to student model

**Optional:**
- **--dataset-size**: Number of samples to evaluate (default: 1000)
- **--log-interval**: Log metrics every N samples (default: 10)
- **--device**: Device to run on (default: auto)

## 📋 **Requirements**

```bash
pip install torch transformers datasets scikit-learn pandas numpy
```

## 🎯 **Usage Examples**

### Start CaseHOLD evaluation:
```bash
python run_casehold.py ./distilled_student/final_student_model
python run_casehold.py ./my_model --dataset-size 500 --log-interval 20 --device cuda:0
```

### Start PubMedQA evaluation:
```bash
python run_pubmed.py ./distilled_student/final_student_model
```

### Start FinQA evaluation:
```bash
python run_finqa.py ./distilled_student/final_student_model
```

### Monitor progress:
```bash
# Watch metrics file in real-time
tail -f benchmark/casehold/student_metrics.csv
tail -f benchmark/pubmed/student_metrics.csv
tail -f benchmark/finqa/student_metrics.csv
```

This creates a clean, focused system for running student model evaluations on each benchmark dataset individually with proper logging and monitoring capabilities.
