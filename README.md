# Distributed Logits Distillation for LLaMA Models

This repository implements a **model-agnostic logits-level knowledge distillation** system that scales down LLaMA2:13B to a 1B parameter student model using distributed processing via gRPC.

## Key Features

### 🎯 **Logits-Level Knowledge Distillation**
- **Soft-target distillation**: Uses KL divergence between teacher and student logits at temperature T
- **Online distillation**: Teacher logits are computed on-the-fly via gRPC (no pre-computation)
- **Weighted loss**: Combines KL divergence loss with original cross-entropy loss using alpha parameter

### 🌐 **Distributed Architecture**
- **gRPC-based**: Multiple teacher servers can run in parallel across different machines
- **Load balancing**: Round-robin distribution of requests across teacher servers
- **Scalable**: Easy to add more teacher servers to increase throughput

### 📊 **Comprehensive Evaluation**
- **Three benchmark datasets**: CaseHOLD (legal), PubMedQA (biomedical), FinQA (financial)
- **Automated metrics**: Accuracy, Precision, Recall, F1-score comparison
- **Visualization**: Performance comparison charts and detailed reports

## Architecture Overview

```
┌─────────────────┐    gRPC     ┌─────────────────┐
│   Student       │◄───────────►│   Teacher       │
│   Trainer       │             │   Server 1      │
│   (1B params)   │             │   (13B params)  │
└─────────────────┘             └─────────────────┘
         │                               │
         │                      ┌─────────────────┐
         │         gRPC         │   Teacher       │
         └─────────────────────►│   Server 2      │
                                │   (13B params)  │
                                └─────────────────┘
```

## Core Components

### 1. **Teacher Server** (`teacher_server.py`)
- Hosts LLaMA2:13B model
- Serves logits via gRPC
- Supports multiple concurrent requests
- Automatic device management (GPU/CPU)

### 2. **Student Trainer** (`student_trainer.py`)
- Extends `trl.SFTTrainer` with distillation loss
- Fetches teacher logits online during training
- Implements KL divergence loss computation
- Handles vocabulary size mismatches

### 3. **Distributed Client** (`distributed_teacher_client.py`)
- Manages multiple teacher server connections
- Round-robin load balancing
- Automatic failover handling

### 4. **Student Inference** (`student.py`)
- Standalone inference script for distilled models
- Evaluates on all three benchmark datasets
- Generates comprehensive metrics

### 5. **Metrics Comparison** (`metrics_comparison.py`)
- Compares teacher vs student performance
- Creates visualization charts
- Generates detailed reports

## Quick Start

### 1. Setup Environment
```bash
pip install -r requirements.txt
```

### 2. Start Teacher Servers
```bash
# Server 1
python teacher_server.py --model meta-llama/Llama-2-13b-hf --port 50051

# Server 2 (optional, on different machine)
python teacher_server.py --model meta-llama/Llama-2-13b-hf --port 50051
```

### 3. Update Server Addresses
Edit `distill_and_evaluate.py`:
```python
TEACHER_SERVERS = [
    "your-server-1:50051",
    "your-server-2:50051",
]
```

### 4. Run Complete Pipeline
```bash
python distill_and_evaluate.py
```

This will:
1. **Load benchmark datasets** (CaseHOLD, PubMedQA, FinQA) for distillation training
2. **Train the student model** using distributed logits distillation on these datasets
3. **Save the distilled model** to `./distilled_student/final_student_model`
4. **Run inference** on all benchmark datasets using the distilled model
5. **Generate performance comparison** reports between teacher and student models

## Distillation Process

### Training Data
The student model is trained on the **same benchmark datasets** it will be evaluated on:

1. **CaseHOLD**: Legal case holding identification tasks
2. **PubMedQA**: Biomedical question answering tasks
3. **FinQA**: Financial numerical reasoning tasks

Each dataset is formatted as instruction-following examples with context, question, and answer.

### Loss Function
The student model is trained with a weighted combination of:

1. **KL Divergence Loss** (soft targets):
   ```
   L_KD = KL(softmax(student_logits/T), softmax(teacher_logits/T)) * T²
   ```

2. **Cross-Entropy Loss** (hard targets):
   ```
   L_CE = CrossEntropy(student_logits, true_labels)
   ```

3. **Combined Loss**:
   ```
   L_total = α * L_KD + (1-α) * L_CE
   ```

### Key Parameters
- **Temperature (T)**: Controls softness of probability distributions (default: 2.0)
- **Alpha (α)**: Balances KL loss vs CE loss (default: 0.5)
- **Batch Size**: Optimized for memory efficiency (default: 1 with gradient accumulation)

## Dataset Evaluation

### CaseHOLD (Legal)
- **Task**: Multiple choice legal reasoning
- **Metric**: Accuracy on holding statement selection
- **Format**: 5-way classification (0-4)

### PubMedQA (Biomedical)
- **Task**: Biomedical question answering
- **Metric**: Accuracy on yes/no/maybe classification
- **Format**: 3-way classification

### FinQA (Financial)
- **Task**: Financial numerical reasoning
- **Metric**: Numerical accuracy with tolerance
- **Format**: Numerical answer extraction

## Configuration

### Training Arguments
```python
TRAINING_ARGS = {
    "num_train_epochs": 3,
    "per_device_train_batch_size": 1,
    "gradient_accumulation_steps": 8,
    "learning_rate": 2e-5,
    "weight_decay": 0.05,
    "warmup_ratio": 0.1,
    "lr_scheduler_type": "cosine",
    "bf16": True,
}
```

### Distillation Arguments
```python
DISTILLATION_ARGS = {
    "temperature": 2.0,  # Softmax temperature
    "alpha": 0.5,        # KL loss weight
}
```

## Output Structure

```
./
├── distilled_student/
│   └── final_student_model/     # Saved student model
├── student_metrics/             # Student evaluation results
│   ├── casehold_student_metrics.json
│   ├── pubmed_student_metrics.json
│   ├── finqa_student_metrics.json
│   └── combined_student_metrics.json
└── comparison_results/          # Teacher vs Student comparison
    ├── teacher_student_comparison.csv
    ├── detailed_comparison.json
    └── performance_comparison.png
```

## Performance Metrics

The system tracks:
- **Accuracy**: Percentage of correct predictions
- **Precision/Recall/F1**: Detailed classification metrics
- **Performance Retention**: Student performance as % of teacher performance
- **Invalid Cases**: Failed predictions requiring manual review

## Customization

### Adding New Datasets
1. Create evaluator class in `student.py`
2. Add dataset to `DATASETS` list in `distill_and_evaluate.py`
3. Update metrics comparison logic

### Modifying Distillation
- Adjust temperature and alpha in `DISTILLATION_ARGS`
- Modify loss computation in `DistributedLogitsTrainer.distillation_loss()`
- Add custom sampling strategies

### Scaling Teacher Servers
- Add server addresses to `TEACHER_SERVERS`
- Implement custom load balancing in `DistributedTeacherClient`

## Requirements

- Python 3.8+
- PyTorch 2.0+
- Transformers 4.30+
- gRPC
- Datasets
- TRL (Transformer Reinforcement Learning)
- scikit-learn
- pandas
- matplotlib
- seaborn

## Notes

- **Memory Requirements**: Teacher servers need ~26GB GPU memory for LLaMA2:13B
- **Training Time**: Depends on dataset size and number of teacher servers
- **Network Latency**: gRPC calls add overhead; use fast network connections
- **Model Compatibility**: Works with any HuggingFace causal language model

## Citation

This implementation is based on the knowledge distillation principles from:
- Hinton et al. "Distilling the Knowledge in a Neural Network" (2015)
- Online distillation techniques for large language models
