# Distributed Logits Distillation for LLaMA Models

This repository implements a **model-agnostic logits-level knowledge distillation** system that scales down LLaMA2:13B to a 1B parameter student model using distributed processing via gRPC.

## Key Features

### 🎯 **Logits-Level Knowledge Distillation**
- **Soft-target distillation**: Uses KL divergence between teacher and student logits at temperature T
- **Online distillation**: Teacher logits are computed on-the-fly via gRPC (no pre-computation)
- **Weighted loss**: Combines KL divergence loss with original cross-entropy loss using alpha parameter

### 🌐 **Distributed Architecture**
- **gRPC-based**: Multiple teacher servers can run in parallel across different machines
- **Load balancing**: Round-robin distribution of requests across teacher servers
- **Scalable**: Easy to add more teacher servers to increase throughput

### 📊 **Comprehensive Evaluation**
- **Three benchmark datasets**: CaseHOLD (legal), PubMedQA (biomedical), FinQA (financial)
- **Automated metrics**: Accuracy, Precision, Recall, F1-score comparison
- **Visualization**: Performance comparison charts and detailed reports

## Architecture Overview

```
┌─────────────────┐    gRPC     ┌─────────────────┐
│   Student       │◄───────────►│   Teacher       │
│   Trainer       │             │   Server 1      │
│   (1B params)   │             │   (13B params)  │
└─────────────────┘             └─────────────────┘
         │                               │
         │                      ┌─────────────────┐
         │         gRPC         │   Teacher       │
         └─────────────────────►│   Server 2      │
                                │   (13B params)  │
                                └─────────────────┘
```

## Core Components

### **Distillation Training** (Optional)
- **Teacher Server** (`teacher_server.py`): Hosts LLaMA2:13B model, serves logits via gRPC
- **Student Trainer** (`student_trainer.py`): Extends `trl.SFTTrainer` with distillation loss
- **Distributed Client** (`distributed_teacher_client.py`): Manages multiple teacher servers
- **Main Pipeline** (`distill_and_evaluate.py`): Complete training pipeline

### **Evaluation System** (Main Use Case)
- **Evaluators** (`evaluators.py`): Clean, modular evaluators for each benchmark dataset
- **Model Manager** (`model_manager.py`): Unified model loading and management
- **Metrics Manager** (`metrics_manager.py`): Centralized metrics storage and comparison
- **Evaluation Runner** (`run_evaluation.py`): Simple CLI for running evaluations
- **Performance Comparison** (`compare_performance.py`): Teacher vs student comparison tools

## Quick Start

### 1. Setup Environment
```bash
pip install -r requirements.txt
```

### 2. For Distillation Training (Optional)
If you want to train a new student model:

```bash
# Start teacher servers
python teacher_server.py --model meta-llama/Llama-2-13b-hf --port 50051

# Update server addresses in distill_and_evaluate.py, then run:
python distill_and_evaluate.py
```

### 3. Evaluate Student Models (Main Use Case)

#### Evaluate on single dataset:
```bash
python run_evaluation.py --model ./distilled_student/final_student_model --dataset casehold
```

#### Evaluate on all datasets:
```bash
python run_evaluation.py --model ./distilled_student/final_student_model --all-datasets
```

#### Compare teacher vs student:
```bash
python compare_performance.py --teacher-vs-student final_student_model
```

#### Compare multiple models:
```bash
python compare_performance.py --compare-students model1 model2 model3
```

### 4. View Results
All metrics are saved to `./metrics/` with detailed comparisons and visualizations.

## Distillation Process

### Training Data
The student model is trained on the **same benchmark datasets** it will be evaluated on:

1. **CaseHOLD**: Legal case holding identification tasks
2. **PubMedQA**: Biomedical question answering tasks
3. **FinQA**: Financial numerical reasoning tasks

Each dataset is formatted as instruction-following examples with context, question, and answer.

### Loss Function
The student model is trained with a weighted combination of:

1. **KL Divergence Loss** (soft targets):
   ```
   L_KD = KL(softmax(student_logits/T), softmax(teacher_logits/T)) * T²
   ```

2. **Cross-Entropy Loss** (hard targets):
   ```
   L_CE = CrossEntropy(student_logits, true_labels)
   ```

3. **Combined Loss**:
   ```
   L_total = α * L_KD + (1-α) * L_CE
   ```

### Key Parameters
- **Temperature (T)**: Controls softness of probability distributions (default: 2.0)
- **Alpha (α)**: Balances KL loss vs CE loss (default: 0.5)
- **Batch Size**: Optimized for memory efficiency (default: 1 with gradient accumulation)

## Dataset Evaluation

### CaseHOLD (Legal)
- **Task**: Multiple choice legal reasoning
- **Metric**: Accuracy on holding statement selection
- **Format**: 5-way classification (0-4)

### PubMedQA (Biomedical)
- **Task**: Biomedical question answering
- **Metric**: Accuracy on yes/no/maybe classification
- **Format**: 3-way classification

### FinQA (Financial)
- **Task**: Financial numerical reasoning
- **Metric**: Numerical accuracy with tolerance
- **Format**: Numerical answer extraction

## Configuration

### Training Arguments
```python
TRAINING_ARGS = {
    "num_train_epochs": 3,
    "per_device_train_batch_size": 1,
    "gradient_accumulation_steps": 8,
    "learning_rate": 2e-5,
    "weight_decay": 0.05,
    "warmup_ratio": 0.1,
    "lr_scheduler_type": "cosine",
    "bf16": True,
}
```

### Distillation Arguments
```python
DISTILLATION_ARGS = {
    "temperature": 2.0,  # Softmax temperature
    "alpha": 0.5,        # KL loss weight
}
```

## Output Structure

```
./
├── distilled_student/
│   └── final_student_model/     # Saved student model
├── student_metrics/             # Student evaluation results
│   ├── casehold_student_metrics.json
│   ├── pubmed_student_metrics.json
│   ├── finqa_student_metrics.json
│   └── combined_student_metrics.json
└── comparison_results/          # Teacher vs Student comparison
    ├── teacher_student_comparison.csv
    ├── detailed_comparison.json
    └── performance_comparison.png
```

## Performance Metrics

The system tracks:
- **Accuracy**: Percentage of correct predictions
- **Precision/Recall/F1**: Detailed classification metrics
- **Performance Retention**: Student performance as % of teacher performance
- **Invalid Cases**: Failed predictions requiring manual review

## Customization

### Adding New Datasets
1. Create evaluator class in `student.py`
2. Add dataset to `DATASETS` list in `distill_and_evaluate.py`
3. Update metrics comparison logic

### Modifying Distillation
- Adjust temperature and alpha in `DISTILLATION_ARGS`
- Modify loss computation in `DistributedLogitsTrainer.distillation_loss()`
- Add custom sampling strategies

### Scaling Teacher Servers
- Add server addresses to `TEACHER_SERVERS`
- Implement custom load balancing in `DistributedTeacherClient`

## Requirements

- Python 3.8+
- PyTorch 2.0+
- Transformers 4.30+
- gRPC
- Datasets
- TRL (Transformer Reinforcement Learning)
- scikit-learn
- pandas
- matplotlib
- seaborn

## Notes

- **Memory Requirements**: Teacher servers need ~26GB GPU memory for LLaMA2:13B
- **Training Time**: Depends on dataset size and number of teacher servers
- **Network Latency**: gRPC calls add overhead; use fast network connections
- **Model Compatibility**: Works with any HuggingFace causal language model

## Citation

This implementation is based on the knowledge distillation principles from:
- Hinton et al. "Distilling the Knowledge in a Neural Network" (2015)
- Online distillation techniques for large language models
