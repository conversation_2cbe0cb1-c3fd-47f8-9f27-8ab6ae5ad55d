# Student Model Evaluation on Benchmark Datasets

Simple system to run student model inference on benchmark datasets (CaseHOLD, PubMedQA, FinQA) with incremental logging.

## 🎯 **Key Features**

- **Individual dataset evaluation**: Separate scripts for each benchmark dataset
- **Incremental logging**: Metrics logged every 10 samples to CSV files
- **Tmux integration**: Run evaluations in separate tmux sessions
- **Real-time monitoring**: Track progress across all evaluations

## 📁 **File Structure**

```
./
├── benchmark/
│   ├── casehold/
│   │   ├── student.py          # CaseHOLD evaluation script
│   │   └── teacher.py          # Teacher baseline (existing)
│   ├── pubmed/
│   │   ├── student.py          # PubMedQA evaluation script
│   │   └── teacher.py          # Teacher baseline (existing)
│   └── finqa/
│       ├── student.py          # FinQA evaluation script
│       └── teacher.py          # Teacher baseline (existing)
├── run_casehold_evaluation.sh  # CaseHOLD tmux runner
├── run_pubmed_evaluation.sh    # PubMedQA tmux runner
├── run_finqa_evaluation.sh     # FinQA tmux runner
└── run_all_evaluations.sh     # Run all evaluations
```

## 🚀 **Quick Start**

### 1. Run Single Dataset Evaluation

#### CaseHOLD (Legal reasoning):
```bash
./run_casehold_evaluation.sh ./path/to/student/model 1000 10
```

#### PubMedQA (Biomedical QA):
```bash
./run_pubmed_evaluation.sh ./path/to/student/model 1000 10
```

#### FinQA (Financial reasoning):
```bash
./run_finqa_evaluation.sh ./path/to/student/model 1000 10
```

### 2. Run All Evaluations in Parallel
```bash
./run_all_evaluations.sh ./path/to/student/model 1000 10
```

### 3. Monitor Progress
```bash
# Attach to specific evaluation
tmux attach-session -t casehold_eval
tmux attach-session -t pubmed_eval
tmux attach-session -t finqa_eval

# Check metrics files
tail -f benchmark/casehold/student_metrics.csv
tail -f benchmark/pubmed/student_metrics.csv
tail -f benchmark/finqa/student_metrics.csv
```

## Distillation Process

### Training Data
The student model is trained on the **same benchmark datasets** it will be evaluated on:

1. **CaseHOLD**: Legal case holding identification tasks
2. **PubMedQA**: Biomedical question answering tasks
3. **FinQA**: Financial numerical reasoning tasks

Each dataset is formatted as instruction-following examples with context, question, and answer.

### Loss Function
The student model is trained with a weighted combination of:

1. **KL Divergence Loss** (soft targets):
   ```
   L_KD = KL(softmax(student_logits/T), softmax(teacher_logits/T)) * T²
   ```

2. **Cross-Entropy Loss** (hard targets):
   ```
   L_CE = CrossEntropy(student_logits, true_labels)
   ```

3. **Combined Loss**:
   ```
   L_total = α * L_KD + (1-α) * L_CE
   ```

### Key Parameters
- **Temperature (T)**: Controls softness of probability distributions (default: 2.0)
- **Alpha (α)**: Balances KL loss vs CE loss (default: 0.5)
- **Batch Size**: Optimized for memory efficiency (default: 1 with gradient accumulation)

## Dataset Evaluation

### CaseHOLD (Legal)
- **Task**: Multiple choice legal reasoning
- **Metric**: Accuracy on holding statement selection
- **Format**: 5-way classification (0-4)

### PubMedQA (Biomedical)
- **Task**: Biomedical question answering
- **Metric**: Accuracy on yes/no/maybe classification
- **Format**: 3-way classification

### FinQA (Financial)
- **Task**: Financial numerical reasoning
- **Metric**: Numerical accuracy with tolerance
- **Format**: Numerical answer extraction

## Configuration

### Training Arguments
```python
TRAINING_ARGS = {
    "num_train_epochs": 3,
    "per_device_train_batch_size": 1,
    "gradient_accumulation_steps": 8,
    "learning_rate": 2e-5,
    "weight_decay": 0.05,
    "warmup_ratio": 0.1,
    "lr_scheduler_type": "cosine",
    "bf16": True,
}
```

### Distillation Arguments
```python
DISTILLATION_ARGS = {
    "temperature": 2.0,  # Softmax temperature
    "alpha": 0.5,        # KL loss weight
}
```

## Output Structure

```
./
├── distilled_student/
│   └── final_student_model/     # Saved student model
├── student_metrics/             # Student evaluation results
│   ├── casehold_student_metrics.json
│   ├── pubmed_student_metrics.json
│   ├── finqa_student_metrics.json
│   └── combined_student_metrics.json
└── comparison_results/          # Teacher vs Student comparison
    ├── teacher_student_comparison.csv
    ├── detailed_comparison.json
    └── performance_comparison.png
```

## Performance Metrics

The system tracks:
- **Accuracy**: Percentage of correct predictions
- **Precision/Recall/F1**: Detailed classification metrics
- **Performance Retention**: Student performance as % of teacher performance
- **Invalid Cases**: Failed predictions requiring manual review

## Customization

### Adding New Datasets
1. Create evaluator class in `student.py`
2. Add dataset to `DATASETS` list in `distill_and_evaluate.py`
3. Update metrics comparison logic

### Modifying Distillation
- Adjust temperature and alpha in `DISTILLATION_ARGS`
- Modify loss computation in `DistributedLogitsTrainer.distillation_loss()`
- Add custom sampling strategies

### Scaling Teacher Servers
- Add server addresses to `TEACHER_SERVERS`
- Implement custom load balancing in `DistributedTeacherClient`

## Requirements

- Python 3.8+
- PyTorch 2.0+
- Transformers 4.30+
- gRPC
- Datasets
- TRL (Transformer Reinforcement Learning)
- scikit-learn
- pandas
- matplotlib
- seaborn

## Notes

- **Memory Requirements**: Teacher servers need ~26GB GPU memory for LLaMA2:13B
- **Training Time**: Depends on dataset size and number of teacher servers
- **Network Latency**: gRPC calls add overhead; use fast network connections
- **Model Compatibility**: Works with any HuggingFace causal language model

## Citation

This implementation is based on the knowledge distillation principles from:
- Hinton et al. "Distilling the Knowledge in a Neural Network" (2015)
- Online distillation techniques for large language models
