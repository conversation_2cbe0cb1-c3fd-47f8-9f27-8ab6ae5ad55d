#!/usr/bin/env python3
"""
Metrics Manager
Clean interface for saving, loading, and comparing evaluation metrics.
"""

import os
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import Dict, List, Any, Optional

class MetricsManager:
    """Manages evaluation metrics storage and comparison"""
    
    def __init__(self, output_dir: str = "./metrics"):
        """
        Initialize metrics manager
        
        Args:
            output_dir: Directory to store metrics
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def save_metrics(self, metrics: Dict[str, Any], model_name: str, dataset_name: str) -> str:
        """
        Save metrics for a model on a dataset
        
        Args:
            metrics: Metrics dictionary
            model_name: Name of the model
            dataset_name: Name of the dataset
            
        Returns:
            Path to saved metrics file
        """
        # Add metadata
        metrics_with_meta = {
            "model_name": model_name,
            "dataset": dataset_name,
            "timestamp": datetime.now().isoformat(),
            **metrics
        }
        
        # Save to JSON
        filename = f"{model_name}_{dataset_name}_metrics.json"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w') as f:
            json.dump(metrics_with_meta, f, indent=2)
        
        print(f"Metrics saved to: {filepath}")
        return filepath
    
    def load_metrics(self, model_name: str, dataset_name: str) -> Optional[Dict[str, Any]]:
        """
        Load metrics for a model on a dataset
        
        Args:
            model_name: Name of the model
            dataset_name: Name of the dataset
            
        Returns:
            Metrics dictionary or None if not found
        """
        filename = f"{model_name}_{dataset_name}_metrics.json"
        filepath = os.path.join(self.output_dir, filename)
        
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                return json.load(f)
        return None
    
    def list_available_metrics(self) -> List[Dict[str, str]]:
        """
        List all available metrics files
        
        Returns:
            List of dictionaries with model and dataset info
        """
        available = []
        
        for filename in os.listdir(self.output_dir):
            if filename.endswith('_metrics.json'):
                parts = filename.replace('_metrics.json', '').split('_')
                if len(parts) >= 2:
                    model_name = '_'.join(parts[:-1])
                    dataset_name = parts[-1]
                    available.append({
                        "model_name": model_name,
                        "dataset": dataset_name,
                        "filename": filename
                    })
        
        return available
    
    def compare_models(self, model_names: List[str], datasets: List[str] = None) -> pd.DataFrame:
        """
        Compare metrics across multiple models
        
        Args:
            model_names: List of model names to compare
            datasets: List of datasets to include (None for all)
            
        Returns:
            DataFrame with comparison results
        """
        if datasets is None:
            datasets = ['casehold', 'pubmed', 'finqa']
        
        comparison_data = []
        
        for model_name in model_names:
            for dataset in datasets:
                metrics = self.load_metrics(model_name, dataset)
                if metrics:
                    comparison_data.append({
                        'Model': model_name,
                        'Dataset': dataset.upper(),
                        'Accuracy (%)': f"{metrics.get('accuracy', 0):.2f}",
                        'Precision (%)': f"{metrics.get('precision', 0):.2f}",
                        'Recall (%)': f"{metrics.get('recall', 0):.2f}",
                        'F1 Score (%)': f"{metrics.get('f1_score', 0):.2f}",
                        'Valid Cases': f"{metrics.get('total_valid', 0)}/{metrics.get('total_cases', 0)}",
                        'Invalid Cases': metrics.get('invalid_cases', 0)
                    })
        
        return pd.DataFrame(comparison_data)
    
    def create_comparison_chart(self, model_names: List[str], datasets: List[str] = None, 
                              metric: str = 'accuracy') -> str:
        """
        Create comparison chart for models
        
        Args:
            model_names: List of model names
            datasets: List of datasets
            metric: Metric to plot ('accuracy', 'f1_score', etc.)
            
        Returns:
            Path to saved chart
        """
        if datasets is None:
            datasets = ['casehold', 'pubmed', 'finqa']
        
        # Collect data
        data = []
        for model_name in model_names:
            for dataset in datasets:
                metrics = self.load_metrics(model_name, dataset)
                if metrics:
                    data.append({
                        'Model': model_name,
                        'Dataset': dataset.upper(),
                        'Value': metrics.get(metric, 0)
                    })
        
        if not data:
            print("No data available for comparison")
            return None
        
        df = pd.DataFrame(data)
        
        # Create chart
        plt.figure(figsize=(12, 6))
        sns.barplot(data=df, x='Dataset', y='Value', hue='Model')
        plt.title(f'{metric.replace("_", " ").title()} Comparison Across Models')
        plt.ylabel(f'{metric.replace("_", " ").title()} (%)')
        plt.legend(title='Model')
        plt.tight_layout()
        
        # Save chart
        chart_path = os.path.join(self.output_dir, f'{metric}_comparison.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Comparison chart saved to: {chart_path}")
        return chart_path
    
    def generate_summary_report(self, model_names: List[str]) -> str:
        """
        Generate a comprehensive summary report
        
        Args:
            model_names: List of model names to include
            
        Returns:
            Path to saved report
        """
        report_lines = []
        report_lines.append("="*80)
        report_lines.append("MODEL PERFORMANCE SUMMARY REPORT")
        report_lines.append("="*80)
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # Get comparison table
        df = self.compare_models(model_names)
        if not df.empty:
            report_lines.append("DETAILED METRICS COMPARISON")
            report_lines.append("-"*40)
            report_lines.append(df.to_string(index=False))
            report_lines.append("")
        
        # Calculate averages per model
        report_lines.append("AVERAGE PERFORMANCE PER MODEL")
        report_lines.append("-"*40)
        
        for model_name in model_names:
            accuracies = []
            f1_scores = []
            
            for dataset in ['casehold', 'pubmed', 'finqa']:
                metrics = self.load_metrics(model_name, dataset)
                if metrics:
                    accuracies.append(metrics.get('accuracy', 0))
                    f1_scores.append(metrics.get('f1_score', 0))
            
            if accuracies:
                avg_accuracy = sum(accuracies) / len(accuracies)
                avg_f1 = sum(f1_scores) / len(f1_scores)
                report_lines.append(f"{model_name}:")
                report_lines.append(f"  Average Accuracy: {avg_accuracy:.2f}%")
                report_lines.append(f"  Average F1 Score: {avg_f1:.2f}%")
                report_lines.append(f"  Datasets Evaluated: {len(accuracies)}/3")
                report_lines.append("")
        
        # Save report
        report_path = os.path.join(self.output_dir, "summary_report.txt")
        with open(report_path, 'w') as f:
            f.write('\n'.join(report_lines))
        
        print(f"Summary report saved to: {report_path}")
        return report_path
    
    def export_to_csv(self, model_names: List[str]) -> str:
        """
        Export all metrics to CSV
        
        Args:
            model_names: List of model names
            
        Returns:
            Path to CSV file
        """
        df = self.compare_models(model_names)
        csv_path = os.path.join(self.output_dir, "all_metrics.csv")
        df.to_csv(csv_path, index=False)
        
        print(f"Metrics exported to CSV: {csv_path}")
        return csv_path

def load_teacher_metrics(benchmark_dir: str = "./benchmark") -> Dict[str, Dict[str, float]]:
    """
    Load teacher metrics from benchmark directory
    
    Args:
        benchmark_dir: Path to benchmark directory
        
    Returns:
        Dictionary of teacher metrics by dataset
    """
    teacher_metrics = {}
    datasets = ['casehold', 'pubmed', 'finqa']
    
    for dataset in datasets:
        dataset_path = os.path.join(benchmark_dir, dataset)
        
        # Look for teacher metrics files
        metrics_files = []
        if os.path.exists(dataset_path):
            for file in os.listdir(dataset_path):
                if 'teacher' in file.lower() and file.endswith('.csv'):
                    metrics_files.append(os.path.join(dataset_path, file))
                elif 'metrics_log.csv' in file:
                    metrics_files.append(os.path.join(dataset_path, file))
        
        if metrics_files:
            # Use the most recent metrics file
            latest_file = max(metrics_files, key=os.path.getmtime)
            try:
                df = pd.read_csv(latest_file)
                if not df.empty:
                    # Get the last row (most recent metrics)
                    last_row = df.iloc[-1]
                    teacher_metrics[dataset] = {
                        'accuracy': float(last_row.get('Accuracy', 0)),
                        'precision': float(last_row.get('Precision', 0)),
                        'recall': float(last_row.get('Recall', 0)),
                        'f1_score': float(last_row.get('F1 Score', 0)),
                        'f1_macro': float(last_row.get('Macro F1 Score', last_row.get('F1 Score', 0))),
                        'invalid_cases': int(last_row.get('Invalid Cases', 0))
                    }
            except Exception as e:
                print(f"Error loading teacher metrics for {dataset}: {e}")
    
    return teacher_metrics
