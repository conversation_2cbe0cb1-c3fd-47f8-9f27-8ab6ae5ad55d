import grpc
from concurrent import futures
import torch
import torch.nn.functional as F
from transformers import AutoModelForCausalLM, AutoTokenizer
import numpy as np
import json
import pickle
from logits_service_pb2 import LogitsResponse
import logits_service_pb2_grpc
import base64
from typing import Dict, Any

MAX_MESSAGE_SIZE = 1000 * 1024 * 1024

class TeacherModelService:
    def __init__(self, model_name: str, device: str = "cuda", vocab_sample_size: int = 5000):
        self.device = device
        self.vocab_sample_size = vocab_sample_size

        print(f"Loading teacher model: {model_name}")
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.bfloat16,
            device_map="auto" if torch.cuda.device_count() > 1 else device
        )
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)

        if not self.tokenizer.pad_token:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        self.model.eval()
        self.vocab_size = self.model.config.vocab_size
        print(f"Teacher model loaded on {device}, vocab_size: {self.vocab_size}")

    def get_logits(self, input_ids: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        """Get logits from teacher model with vocabulary sampling"""
        with torch.no_grad():
            # Move inputs to appropriate device
            if hasattr(self.model, 'device'):
                device = self.model.device
            else:
                device = next(self.model.parameters()).device

            inputs = {
                'input_ids': input_ids.to(device),
                'attention_mask': attention_mask.to(device)
            }
            outputs = self.model(**inputs)

            # Apply vocabulary sampling if specified
            logits = outputs.logits
            if self.vocab_sample_size < self.vocab_size:
                logits = self._apply_vocab_sampling(logits)

            return logits.cpu()

    def _apply_vocab_sampling(self, logits: torch.Tensor) -> torch.Tensor:
        """Apply vocabulary sampling to reduce computational overhead"""
        # Get top-k vocabulary indices based on logits magnitude
        batch_size, seq_len, vocab_size = logits.shape

        # Flatten logits to find top-k across all positions
        flat_logits = logits.view(-1, vocab_size)

        # Get top-k indices for each position
        _, top_k_indices = torch.topk(flat_logits.abs(), self.vocab_sample_size, dim=-1)

        # Create mask for sampled vocabulary
        sampled_logits = torch.full_like(logits, float('-inf'))

        for i in range(flat_logits.shape[0]):
            sampled_logits.view(-1, vocab_size)[i, top_k_indices[i]] = flat_logits[i, top_k_indices[i]]

        return sampled_logits

    def serialize_tensor(self, tensor: torch.Tensor) -> str:
        tensor_bytes = pickle.dumps(tensor)
        return base64.b64encode(tensor_bytes).decode('utf-8')

    def deserialize_tensor(self, tensor_str: str) -> torch.Tensor:
        tensor_bytes = base64.b64decode(tensor_str.encode('utf-8'))
        return pickle.loads(tensor_bytes)

class LogitsServiceServicer:
    def __init__(self, teacher_service: TeacherModelService):
        self.teacher_service = teacher_service

    def GetLogits(self, request, context):
        try:
            input_ids = self.teacher_service.deserialize_tensor(request.input_ids)
            attention_mask = self.teacher_service.deserialize_tensor(request.attention_mask)
            logits = self.teacher_service.get_logits(input_ids, attention_mask)
            
            logits_str = self.teacher_service.serialize_tensor(logits)
            
            return LogitsResponse(
                logits=logits_str,
                success=True,
                error_message=""
            )
        except Exception as e:
            return LogitsResponse(
                logits="",
                success=False,
                error_message=str(e)
            )

def serve_teacher_model(model_name: str, port: int = 50051, vocab_sample_size: int = 5000):
    teacher_service = TeacherModelService(model_name, vocab_sample_size=vocab_sample_size)
    server = grpc.server(
        futures.ThreadPoolExecutor(max_workers=10),
        options=[
            ('grpc.max_send_message_length', 512 * 1024 * 1024),
            ('grpc.max_receive_message_length', 512 * 1024 * 1024)
        ]
    )
    servicer = LogitsServiceServicer(teacher_service)
    logits_service_pb2_grpc.add_LogitsServiceServicer_to_server(servicer, server)
    server.add_insecure_port(f'[::]:{port}')
    server.start()

    print(f"Teacher model server started on port {port} with vocab sampling: {vocab_sample_size}")
    try:
        server.wait_for_termination()
    except KeyboardInterrupt:
        print("Shutting down teacher server...")
        server.stop(0)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Start teacher model server')
    parser.add_argument('--model', default="meta-llama/Llama-2-13b-hf", help='Teacher model name')
    parser.add_argument('--port', type=int, default=50051, help='Server port')
    parser.add_argument('--vocab-sample-size', type=int, default=5000, help='Vocabulary sampling size')

    args = parser.parse_args()
    serve_teacher_model(args.model, args.port, args.vocab_sample_size)
