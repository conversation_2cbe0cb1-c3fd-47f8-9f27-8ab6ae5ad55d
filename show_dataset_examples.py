#!/usr/bin/env python3
"""
Show Dataset Examples
Displays how the benchmark datasets are formatted for distillation training.
"""

from datasets import load_dataset

def show_casehold_example():
    """Show how CaseHOLD data is formatted"""
    print("="*60)
    print("CASEHOLD DATASET EXAMPLE")
    print("="*60)
    
    try:
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "casehold")
        example = ds['train'][0]
        
        print("Raw data:")
        print(f"  citing_prompt: {example['citing_prompt'][:200]}...")
        print(f"  holding_0: {example['holding_0'][:100]}...")
        print(f"  holding_1: {example['holding_1'][:100]}...")
        print(f"  label: {example['label']}")
        
        print("\nFormatted for training:")
        formatted = f"""Given the following legal case context and holding statements, select the correct holding.

Context: {example['citing_prompt']}

Holdings:
0: {example['holding_0']}
1: {example['holding_1']}
2: {example['holding_2']}
3: {example['holding_3']}
4: {example['holding_4']}

The correct holding is: {example['label']}"""
        
        print(formatted[:500] + "..." if len(formatted) > 500 else formatted)
        
    except Exception as e:
        print(f"Error loading CaseHOLD: {e}")

def show_pubmed_example():
    """Show how PubMedQA data is formatted"""
    print("\n" + "="*60)
    print("PUBMEDQA DATASET EXAMPLE")
    print("="*60)
    
    try:
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "pubmedqa")
        example = ds['train'][0]
        
        print("Raw data:")
        print(f"  question: {example['question']}")
        print(f"  context: {example['context'][:200]}...")
        print(f"  final_decision: {example['final_decision']}")
        
        print("\nFormatted for training:")
        formatted = f"""Answer the following biomedical question based on the provided context.

Question: {example['question']}

Context: {example['context']}

Long Answer: {example['long_answer']}

Final Decision: {example['final_decision']}"""
        
        print(formatted[:500] + "..." if len(formatted) > 500 else formatted)
        
    except Exception as e:
        print(f"Error loading PubMedQA: {e}")

def show_finqa_example():
    """Show how FinQA data is formatted"""
    print("\n" + "="*60)
    print("FINQA DATASET EXAMPLE")
    print("="*60)
    
    try:
        ds = load_dataset("MothMalone/SLMS-KD-Benchmarks", "finqa")
        example = ds['train'][0]
        
        print("Raw data:")
        print(f"  question: {example['question']}")
        context = example.get('context', example.get('pre_text', ''))
        print(f"  context: {context[:200]}...")
        answer = example.get('answer', example.get('exe_ans', ''))
        print(f"  answer: {answer}")
        
        print("\nFormatted for training:")
        table = example.get('table', '')
        formatted = f"""Solve the following financial question using the provided context and table.

Question: {example['question']}

Context: {context}

Table: {table}

Answer: {answer}"""
        
        print(formatted[:500] + "..." if len(formatted) > 500 else formatted)
        
    except Exception as e:
        print(f"Error loading FinQA: {e}")

def show_dataset_statistics():
    """Show statistics about the combined dataset"""
    print("\n" + "="*60)
    print("DATASET STATISTICS")
    print("="*60)
    
    total_examples = 0
    
    datasets = [
        ("CaseHOLD", "MothMalone/SLMS-KD-Benchmarks", "casehold"),
        ("PubMedQA", "MothMalone/SLMS-KD-Benchmarks", "pubmedqa"),
        ("FinQA", "MothMalone/SLMS-KD-Benchmarks", "finqa"),
    ]
    
    for name, dataset_name, config in datasets:
        try:
            ds = load_dataset(dataset_name, config)
            count = len(ds['train'])
            total_examples += count
            print(f"{name:12}: {count:,} examples")
        except Exception as e:
            print(f"{name:12}: Error - {e}")
    
    print(f"{'Total':12}: {total_examples:,} examples")
    print(f"\nThis combined dataset will be used for distillation training.")

def main():
    """Show examples from all benchmark datasets"""
    print("BENCHMARK DATASETS FOR DISTILLATION TRAINING")
    print("This shows how the datasets are formatted for training the student model.")
    
    show_casehold_example()
    show_pubmed_example() 
    show_finqa_example()
    show_dataset_statistics()
    
    print("\n" + "="*60)
    print("The student model will be trained on these formatted examples")
    print("using distributed logits distillation from the teacher model.")
    print("="*60)

if __name__ == "__main__":
    main()
