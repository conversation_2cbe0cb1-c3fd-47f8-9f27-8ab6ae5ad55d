#!/usr/bin/env python3
"""
System Test Script
Tests the distributed logits distillation system components.
"""

import os
import sys
import time
import subprocess
import torch
from transformers import AutoTokenizer
import grpc
from student_trainer import RemoteTeacherClient
import json

def test_teacher_server_connection(server_address="localhost:50051"):
    """Test connection to teacher server"""
    print(f"Testing connection to teacher server at {server_address}...")
    
    try:
        client = RemoteTeacherClient(server_address)
        
        # Create test input
        tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-2-1b-hf")
        if not tokenizer.pad_token:
            tokenizer.pad_token = tokenizer.eos_token
            
        test_text = "Hello, this is a test message."
        inputs = tokenizer(test_text, return_tensors="pt", max_length=50, padding="max_length")
        
        # Test teacher logits retrieval
        logits = client.get_teacher_logits(inputs['input_ids'], inputs['attention_mask'])
        
        if logits is not None:
            print(f"✅ Teacher server connection successful!")
            print(f"   Logits shape: {logits.shape}")
            print(f"   Logits dtype: {logits.dtype}")
            return True
        else:
            print("❌ Failed to get logits from teacher server")
            return False
            
    except Exception as e:
        print(f"❌ Teacher server connection failed: {e}")
        return False
    finally:
        try:
            client.close()
        except:
            pass

def test_student_model_loading():
    """Test student model loading"""
    print("Testing student model loading...")
    
    try:
        from transformers import AutoModelForCausalLM
        
        model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-2-1b-hf", 
            torch_dtype=torch.bfloat16
        )
        tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-2-1b-hf")
        
        print(f"✅ Student model loaded successfully!")
        print(f"   Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   Vocab size: {model.config.vocab_size}")
        return True
        
    except Exception as e:
        print(f"❌ Student model loading failed: {e}")
        return False

def test_dataset_loading():
    """Test dataset loading"""
    print("Testing dataset loading...")
    
    try:
        from datasets import load_dataset
        
        # Test benchmark datasets
        datasets_to_test = [
            ("MothMalone/SLMS-KD-Benchmarks", "casehold"),
            ("MothMalone/SLMS-KD-Benchmarks", "pubmedqa"),
            ("MothMalone/SLMS-KD-Benchmarks", "finqa"),
        ]
        
        for dataset_name, config in datasets_to_test:
            try:
                ds = load_dataset(dataset_name, config)
                print(f"   ✅ {config}: {len(ds['train'])} examples")
            except Exception as e:
                print(f"   ❌ {config}: {e}")
        
        # Test combined benchmark dataset loading
        try:
            from distill_and_evaluate import load_benchmark_datasets
            combined_ds = load_benchmark_datasets()
            print(f"   ✅ Combined benchmark datasets: {len(combined_ds)} examples")
        except Exception as e:
            print(f"   ❌ Combined benchmark datasets: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Dataset loading failed: {e}")
        return False

def test_distillation_trainer():
    """Test distillation trainer initialization"""
    print("Testing distillation trainer...")
    
    try:
        from student_trainer import DistributedLogitsTrainer, RemoteTeacherClient
        from transformers import AutoModelForCausalLM, AutoTokenizer, TrainingArguments
        from datasets import Dataset
        
        # Create mock teacher client
        class MockTeacherClient:
            def get_teacher_logits(self, input_ids, attention_mask):
                # Return mock logits with correct shape
                batch_size, seq_len = input_ids.shape
                vocab_size = 32000  # LLaMA vocab size
                return torch.randn(batch_size, seq_len, vocab_size)
            
            def close(self):
                pass
        
        # Load student model
        model = AutoModelForCausalLM.from_pretrained(
            "meta-llama/Llama-2-1b-hf", 
            torch_dtype=torch.bfloat16
        )
        tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-2-1b-hf")
        if not tokenizer.pad_token:
            tokenizer.pad_token = tokenizer.eos_token
        
        # Create mock dataset
        mock_data = {
            "input_ids": [[1, 2, 3, 4, 5] * 10] * 10,  # 10 samples, 50 tokens each
            "attention_mask": [[1] * 50] * 10,
            "labels": [[1, 2, 3, 4, 5] * 10] * 10,
        }
        dataset = Dataset.from_dict(mock_data)
        
        # Create training arguments
        training_args = TrainingArguments(
            output_dir="./test_output",
            num_train_epochs=1,
            per_device_train_batch_size=1,
            logging_steps=1,
            save_steps=1000,
            remove_unused_columns=False,
        )
        
        # Initialize trainer
        trainer = DistributedLogitsTrainer(
            teacher_client=MockTeacherClient(),
            temperature=2.0,
            alpha=0.5,
            model=model,
            train_dataset=dataset,
            args=training_args,
        )
        
        print("✅ Distillation trainer initialized successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Distillation trainer test failed: {e}")
        return False

def test_grpc_proto():
    """Test gRPC protocol buffer compilation"""
    print("Testing gRPC protocol buffers...")
    
    try:
        import logits_service_pb2
        import logits_service_pb2_grpc
        
        # Test creating request/response objects
        request = logits_service_pb2.LogitsRequest()
        response = logits_service_pb2.LogitsResponse()
        
        print("✅ gRPC protocol buffers working!")
        return True
        
    except Exception as e:
        print(f"❌ gRPC protocol buffer test failed: {e}")
        print("   Run: python -m grpc_tools.protoc --python_out=. --grpc_python_out=. logits_service.proto")
        return False

def test_system_dependencies():
    """Test system dependencies"""
    print("Testing system dependencies...")
    
    required_packages = [
        "torch",
        "transformers", 
        "datasets",
        "trl",
        "grpc",
        "sklearn",
        "pandas",
        "matplotlib",
        "seaborn"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "sklearn":
                import sklearn
            elif package == "grpc":
                import grpc
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {missing_packages}")
        print("   Install with: pip install " + " ".join(missing_packages))
        return False
    else:
        print("✅ All dependencies available!")
        return True

def run_all_tests():
    """Run all system tests"""
    print("="*60)
    print("DISTRIBUTED LOGITS DISTILLATION SYSTEM TESTS")
    print("="*60)
    
    tests = [
        ("System Dependencies", test_system_dependencies),
        ("gRPC Protocol Buffers", test_grpc_proto),
        ("Student Model Loading", test_student_model_loading),
        ("Dataset Loading", test_dataset_loading),
        ("Distillation Trainer", test_distillation_trainer),
        ("Teacher Server Connection", test_teacher_server_connection),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for distillation.")
    else:
        print("⚠️  Some tests failed. Please fix issues before running distillation.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
